import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  Card,
  CardContent,
  InputAdornment,
  Chip,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  Payment,
  Person,
  DateRange,
  AttachMoney,
} from '@mui/icons-material';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';

// Components
import { CurrencyInput } from '@/components/CurrencyInput';

// Types
import { EmployeePayment, EmployeePaymentForm, EmployeePaymentFilter, Employee, EmployeeForm } from '@/types';

// Utils
import { format, startOfMonth, endOfMonth, isWithinInterval } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatSingleCurrency, formatDualCurrencyForCard } from '@/utils';
import { validateFinancialInput, ERROR_MESSAGES } from '@/utils/currencyUtils.js';

const EmployeePaymentsPage: React.FC = () => {
  const [employeePayments, setEmployeePayments] = useState<EmployeePayment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<EmployeePayment[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingPayment, setEditingPayment] = useState<EmployeePayment | null>(null);
  const [formData, setFormData] = useState<EmployeePaymentForm>({
    nomEmploye: '',
    montantCDF: 0,
    datePaiement: format(new Date(), 'yyyy-MM-dd'),
    methodePaiement: 'cash',
    notes: '',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [settings, setSettings] = useState<any>({ tauxChangeUSDCDF: 2800 });
  const [loading, setLoading] = useState(false);

  // Employee management state
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [openEmployeeDialog, setOpenEmployeeDialog] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [employeeFormData, setEmployeeFormData] = useState<EmployeeForm>({
    nomComplet: '', // Changed from separate nom/prenom to single nomComplet field
    poste: '',
    salaireCDF: 0,
    dateEmbauche: format(new Date(), 'yyyy-MM-dd'),
    telephone: '',
    adresse: '',
    statut: 'actif',
    notes: '',
  });
  const [activeTab, setActiveTab] = useState(0); // 0 = Payments, 1 = Employees

  const permissions = adaptiveAuthService.getUserPermissions();
  const currentUser = adaptiveAuthService.getCurrentUser();

  // Payment methods - Only three options as requested
  const paymentMethods = [
    { value: 'cash', label: 'Cash' },
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'bank', label: 'Banque' },
  ];

  // Date filter options
  const dateFilterOptions = [
    { value: '', label: 'Toutes les dates' },
    { value: 'today', label: "Aujourd'hui" },
    { value: 'week', label: 'Cette semaine' },
    { value: 'month', label: 'Ce mois' },
    { value: 'year', label: 'Cette année' },
  ];

  // Load data on component mount
  useEffect(() => {
    loadEmployeePayments();
    loadEmployees();
    loadSettings();
  }, []);

  // Filter payments when search term, filters, or payments change
  useEffect(() => {
    filterPayments();
  }, [employeePayments, searchTerm, paymentMethodFilter, dateFilter]);

  const loadEmployeePayments = async () => {
    try {
      setLoading(true);
      const payments = await adaptiveStorageService.getEmployeePayments();
      setEmployeePayments(payments);
    } catch (err) {
      console.error('Error loading employee payments:', err);
      setError('Erreur lors du chargement des paiements employés');
    } finally {
      setLoading(false);
    }
  };

  const loadEmployees = async () => {
    try {
      const employeeList = await adaptiveStorageService.getEmployees();
      setEmployees(employeeList);
    } catch (err) {
      console.error('Error loading employees:', err);
      setError('Erreur lors du chargement des employés');
    }
  };

  const loadSettings = async () => {
    try {
      const settingsData = await adaptiveStorageService.getSettings();
      setSettings(settingsData);
    } catch (err) {
      console.error('Error loading settings:', err);
    }
  };

  const filterPayments = () => {
    let filtered = [...employeePayments];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(payment =>
        payment.nomEmploye.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.notes?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Payment method filter
    if (paymentMethodFilter) {
      filtered = filtered.filter(payment => payment.methodePaiement === paymentMethodFilter);
    }

    // Date filter
    if (dateFilter) {
      const now = new Date();
      let startDate: Date;
      let endDate: Date = now;

      switch (dateFilter) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }

      filtered = filtered.filter(payment => {
        const paymentDate = new Date(payment.datePaiement);
        return isWithinInterval(paymentDate, { start: startDate, end: endDate });
      });
    }

    setFilteredPayments(filtered);
    setPage(0); // Reset to first page when filtering
  };

  const handleOpenDialog = (payment?: EmployeePayment) => {
    if (payment) {
      setEditingPayment(payment);
      setFormData({
        nomEmploye: payment.nomEmploye,
        montantCDF: payment.montantCDF,
        datePaiement: payment.datePaiement,
        methodePaiement: payment.methodePaiement,
        notes: payment.notes || '',
      });
    } else {
      setEditingPayment(null);
      setFormData({
        nomEmploye: '',
        montantCDF: 0,
        datePaiement: format(new Date(), 'yyyy-MM-dd'),
        methodePaiement: 'cash',
        notes: '',
      });
    }
    setOpenDialog(true);
    setError('');
    setSuccess('');
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingPayment(null);
    setError('');
    setSuccess('');
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');

      // Validation using standardized validation
      if (!formData.nomEmploye.trim()) {
        setError('Veuillez sélectionner un employé');
        return;
      }

      // Validate that the selected employee exists and is active
      const selectedEmployee = employees.find(emp =>
        emp.nomComplet === formData.nomEmploye && emp.statut === 'actif'
      );
      if (!selectedEmployee) {
        setError('L\'employé sélectionné n\'est pas valide ou n\'est plus actif');
        return;
      }

      // Validate payment amount using standardized validation
      const amountValidation = validateFinancialInput(formData.montantCDF, 'Le montant du paiement', {
        allowZero: false,
        allowNegative: false
      });

      if (!amountValidation.isValid) {
        setError(amountValidation.errors[0] || ERROR_MESSAGES.NEGATIVE_PRICE);
        return;
      }

      const paymentData: EmployeePayment = {
        id: editingPayment?.id || `emp_pay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        nomEmploye: formData.nomEmploye.trim(),
        montantCDF: formData.montantCDF,
        montantUSD: formData.montantCDF / settings.tauxChangeUSDCDF,
        datePaiement: formData.datePaiement,
        methodePaiement: formData.methodePaiement,
        notes: formData.notes?.trim(),
        creePar: currentUser?.nom || 'Système',
        dateCreation: editingPayment?.dateCreation || new Date().toISOString(),
        dateModification: editingPayment ? new Date().toISOString() : undefined,
      };

      if (editingPayment) {
        await adaptiveStorageService.updateEmployeePayment(paymentData);
        setSuccess('Paiement employé modifié avec succès');
      } else {
        await adaptiveStorageService.addEmployeePayment(paymentData);
        setSuccess('Paiement employé ajouté avec succès');
      }

      await loadEmployeePayments();
      handleCloseDialog();
    } catch (err) {
      console.error('Error saving employee payment:', err);
      setError('Erreur lors de la sauvegarde du paiement employé');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (payment: EmployeePayment) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer le paiement de ${payment.nomEmploye} ?`)) {
      return;
    }

    try {
      setLoading(true);
      await adaptiveStorageService.deleteEmployeePayment(payment.id);
      setSuccess('Paiement employé supprimé avec succès');
      await loadEmployeePayments();
    } catch (err) {
      console.error('Error deleting employee payment:', err);
      setError('Erreur lors de la suppression du paiement employé');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getPaymentMethodLabel = (method: string) => {
    const methodObj = paymentMethods.find(m => m.value === method);
    return methodObj ? methodObj.label : method;
  };

  // Employee management functions
  const handleOpenEmployeeDialog = (employee?: Employee) => {
    if (employee) {
      setEditingEmployee(employee);
      setEmployeeFormData({
        nomComplet: employee.nomComplet, // Changed from separate nom/prenom to single nomComplet field
        poste: employee.poste,
        salaireCDF: employee.salaireCDF,
        dateEmbauche: employee.dateEmbauche,
        telephone: employee.telephone || '',
        adresse: employee.adresse || '',
        statut: employee.statut,
        notes: employee.notes || '',
      });
    } else {
      setEditingEmployee(null);
      setEmployeeFormData({
        nomComplet: '', // Changed from separate nom/prenom to single nomComplet field
        poste: '',
        salaireCDF: 0,
        dateEmbauche: format(new Date(), 'yyyy-MM-dd'),
        telephone: '',
        adresse: '',
        statut: 'actif',
        notes: '',
      });
    }
    setOpenEmployeeDialog(true);
    setError('');
    setSuccess('');
  };

  const handleCloseEmployeeDialog = () => {
    setOpenEmployeeDialog(false);
    setEditingEmployee(null);
    setError('');
    setSuccess('');
  };

  const handleSaveEmployee = async () => {
    if (!employeeFormData.nomComplet.trim() || !employeeFormData.poste.trim()) {
      setError('Le nom complet et le poste sont requis'); // Updated validation message
      return;
    }

    try {
      setLoading(true);
      const now = new Date().toISOString();

      const employeeData: Employee = {
        id: editingEmployee?.id || `emp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        nomComplet: employeeFormData.nomComplet.trim(), // Changed from separate nom/prenom to single nomComplet field
        poste: employeeFormData.poste.trim(),
        salaireCDF: employeeFormData.salaireCDF,
        salaireUSD: employeeFormData.salaireCDF / settings.tauxChangeUSDCDF,
        dateEmbauche: employeeFormData.dateEmbauche,
        telephone: employeeFormData.telephone?.trim(),
        adresse: employeeFormData.adresse?.trim(),
        statut: employeeFormData.statut,
        notes: employeeFormData.notes?.trim(),
        creePar: currentUser?.nom || 'Système',
        dateCreation: editingEmployee?.dateCreation || now,
        dateModification: editingEmployee ? now : undefined,
      };

      if (editingEmployee) {
        await adaptiveStorageService.updateEmployee(employeeData);
        setSuccess('Employé modifié avec succès');
      } else {
        await adaptiveStorageService.addEmployee(employeeData);
        setSuccess('Employé ajouté avec succès');
      }

      await loadEmployees();
      handleCloseEmployeeDialog();
    } catch (err) {
      console.error('Error saving employee:', err);
      setError('Erreur lors de la sauvegarde de l\'employé');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEmployee = async (employee: Employee) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer l'employé ${employee.nomComplet} ?`)) { // Fixed to use nomComplet instead of nom
      return;
    }

    try {
      setLoading(true);
      await adaptiveStorageService.deleteEmployee(employee.id);
      setSuccess('Employé supprimé avec succès');
      await loadEmployees();
    } catch (err) {
      console.error('Error deleting employee:', err);
      setError('Erreur lors de la suppression de l\'employé');
    } finally {
      setLoading(false);
    }
  };

  // Calculate totals
  const totalPaymentsCDF = filteredPayments.reduce((sum, payment) => sum + payment.montantCDF, 0);
  const totalPaymentsUSD = filteredPayments.reduce((sum, payment) => sum + (payment.montantUSD || 0), 0);

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        <Payment sx={{ mr: 1, verticalAlign: 'middle' }} />
        Gestion des Employés et Paiements
      </Typography>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Paiements Employés" />
          <Tab label="Gestion des Employés" />
        </Tabs>
      </Paper>

      {/* Tab Panel 0: Employee Payments */}
      {activeTab === 0 && (
        <>
          {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Paiements
              </Typography>
              <Typography variant="h5">
                {filteredPayments.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Montant Total
              </Typography>
              <Typography variant="h5" fontWeight="medium">
                {formatDualCurrencyForCard(totalPaymentsCDF, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(totalPaymentsCDF, settings.tauxChangeUSDCDF).primaryCurrency}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                ≈ ${formatDualCurrencyForCard(totalPaymentsCDF, settings.tauxChangeUSDCDF).secondaryAmount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Employés Payés
              </Typography>
              <Typography variant="h5">
                {new Set(filteredPayments.map(p => p.nomEmploye)).size}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Filters and Actions */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label="Rechercher employé"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Méthode de paiement</InputLabel>
              <Select
                value={paymentMethodFilter}
                onChange={(e) => setPaymentMethodFilter(e.target.value)}
                label="Méthode de paiement"
              >
                <MenuItem value="">Toutes</MenuItem>
                {paymentMethods.map((method) => (
                  <MenuItem key={method.value} value={method.value}>
                    {method.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Période</InputLabel>
              <Select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                label="Période"
              >
                {dateFilterOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog()}
              disabled={!permissions.canManageEmployeePayments || loading}
              fullWidth
            >
              Nouveau Paiement
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Button
              variant="outlined"
              onClick={() => {
                setSearchTerm('');
                setPaymentMethodFilter('');
                setDateFilter('');
              }}
              fullWidth
            >
              Réinitialiser
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Employee Payments Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Employé</TableCell>
                <TableCell>Montant (CDF)</TableCell>
                <TableCell>Montant (USD)</TableCell>
                <TableCell>Date de Paiement</TableCell>
                <TableCell>Méthode</TableCell>
                <TableCell>Notes</TableCell>
                <TableCell>Créé par</TableCell>
                {permissions.canManageEmployeePayments && <TableCell>Actions</TableCell>}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredPayments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <Typography variant="body2" color="textSecondary">
                      Aucun paiement employé trouvé
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredPayments
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Person sx={{ mr: 1, color: 'primary.main' }} />
                          {payment.nomEmploye}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold" color="primary">
                          {formatSingleCurrency(payment.montantCDF, 'CDF')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="textSecondary">
                          {formatSingleCurrency(payment.montantUSD || 0, 'USD')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <DateRange sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                          {format(new Date(payment.datePaiement), 'dd/MM/yyyy', { locale: fr })}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getPaymentMethodLabel(payment.methodePaiement)}
                          size="small"
                          color={
                            payment.methodePaiement === 'cash' ? 'success' :
                            payment.methodePaiement === 'mobile_money' ? 'info' :
                            'default'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 150 }}>
                          {payment.notes || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="textSecondary">
                          {payment.creePar}
                        </Typography>
                      </TableCell>
                      {permissions.canManageEmployeePayments && (
                        <TableCell>
                          <Tooltip title="Modifier">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDialog(payment)}
                              disabled={loading}
                            >
                              <Edit />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Supprimer">
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(payment)}
                              disabled={loading}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      )}
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          component="div"
          count={filteredPayments.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`
          }
        />
      </Paper>

      {/* Add/Edit Employee Payment Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingPayment ? 'Modifier le Paiement Employé' : 'Nouveau Paiement Employé'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth disabled={loading}>
                  <InputLabel id="employee-select-label">Nom de l'employé *</InputLabel>
                  <Select
                    labelId="employee-select-label"
                    value={formData.nomEmploye}
                    onChange={(e) => setFormData({ ...formData, nomEmploye: e.target.value })}
                    label="Nom de l'employé *"
                    startAdornment={
                      <InputAdornment position="start">
                        <Person />
                      </InputAdornment>
                    }
                  >
                    {/* Show placeholder option when no employee is selected */}
                    <MenuItem value="" disabled>
                      <em>Sélectionner un employé</em>
                    </MenuItem>
                    {/* Filter only active employees and sort alphabetically */}
                    {employees
                      .filter(employee => employee.statut === 'actif')
                      .sort((a, b) => a.nomComplet.localeCompare(b.nomComplet, 'fr', { sensitivity: 'base' }))
                      .map((employee) => (
                        <MenuItem key={employee.id} value={employee.nomComplet}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {employee.nomComplet}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {employee.poste}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    {/* Show message when no active employees are available */}
                    {employees.filter(employee => employee.statut === 'actif').length === 0 && (
                      <MenuItem value="" disabled>
                        <em>Aucun employé actif disponible</em>
                      </MenuItem>
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <CurrencyInput
                  label="Montant du paiement *"
                  value={formData.montantCDF}
                  onChange={(value) => setFormData({ ...formData, montantCDF: value })}
                  currency="CDF"
                  disabled={loading}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Date de paiement *"
                  value={formData.datePaiement}
                  onChange={(e) => setFormData({ ...formData, datePaiement: e.target.value })}
                  disabled={loading}
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <DateRange />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Méthode de paiement *</InputLabel>
                  <Select
                    value={formData.methodePaiement}
                    onChange={(e) => setFormData({ ...formData, methodePaiement: e.target.value as any })}
                    label="Méthode de paiement *"
                    disabled={loading}
                  >
                    {paymentMethods.map((method) => (
                      <MenuItem key={method.value} value={method.value}>
                        {method.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Notes (optionnel)"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  disabled={loading}
                  placeholder="Ajoutez des notes sur ce paiement..."
                />
              </Grid>
              {formData.montantCDF > 0 && (
                <Grid item xs={12}>
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      Équivalent USD (taux: {settings.tauxChangeUSDCDF} CDF/USD)
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {formatSingleCurrency(formData.montantCDF / settings.tauxChangeUSDCDF, 'USD')}
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={loading}>
            Annuler
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={
              loading ||
              !formData.nomEmploye.trim() ||
              formData.montantCDF <= 0 ||
              !employees.find(emp => emp.nomComplet === formData.nomEmploye && emp.statut === 'actif')
            }
            startIcon={loading ? <CircularProgress size={20} /> : <Payment />}
          >
            {editingPayment ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>
        </>
      )}

      {/* Tab Panel 1: Employee Management */}
      {activeTab === 1 && (
        <>
          {/* Employee Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total Employés
                  </Typography>
                  <Typography variant="h5">
                    {employees.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Employés Actifs
                  </Typography>
                  <Typography variant="h5">
                    {employees.filter(emp => emp.statut === 'actif').length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Masse Salariale (CDF)
                  </Typography>
                  <Typography variant="h5">
                    {formatSingleCurrency(
                      employees.filter(emp => emp.statut === 'actif').reduce((sum, emp) => sum + emp.salaireCDF, 0),
                      'CDF'
                    )}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Employee Actions */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">
                Liste des Employés
              </Typography>
              {permissions.canManageEmployeePayments && (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleOpenEmployeeDialog()}
                >
                  Ajouter Employé
                </Button>
              )}
            </Box>
          </Paper>

          {/* Employee Table */}
          <Paper>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Nom</TableCell>
                    <TableCell>Poste</TableCell>
                    <TableCell>Salaire (CDF)</TableCell>
                    <TableCell>Date d'Embauche</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Téléphone</TableCell>
                    {permissions.canManageEmployeePayments && <TableCell>Actions</TableCell>}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {employees.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        <Typography variant="body2" color="textSecondary">
                          Aucun employé trouvé
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    employees.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Person sx={{ mr: 1, color: 'primary.main' }} />
                            {employee.nomComplet} {/* Changed from separate nom/prenom to single nomComplet field */}
                          </Box>
                        </TableCell>
                        <TableCell>{employee.poste}</TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold" color="primary">
                            {formatSingleCurrency(employee.salaireCDF, 'CDF')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {format(new Date(employee.dateEmbauche), 'dd/MM/yyyy', { locale: fr })}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={employee.statut}
                            color={employee.statut === 'actif' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{employee.telephone || '-'}</TableCell>
                        {permissions.canManageEmployeePayments && (
                          <TableCell>
                            <Box display="flex" gap={1}>
                              <Tooltip title="Modifier">
                                <IconButton
                                  size="small"
                                  onClick={() => handleOpenEmployeeDialog(employee)}
                                >
                                  <Edit fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Supprimer">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDeleteEmployee(employee)}
                                >
                                  <Delete fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        )}
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Employee Dialog */}
          <Dialog open={openEmployeeDialog} onClose={handleCloseEmployeeDialog} maxWidth="md" fullWidth>
            <DialogTitle>
              {editingEmployee ? 'Modifier Employé' : 'Ajouter Employé'}
            </DialogTitle>
            <DialogContent>
              {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
              {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Nom Complet *" // Changed from separate "Nom" and "Prénom" to single "Nom Complet" field
                    value={employeeFormData.nomComplet}
                    onChange={(e) => setEmployeeFormData({ ...employeeFormData, nomComplet: e.target.value })}
                    placeholder="Ex: Jean Dupont"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Poste *"
                    value={employeeFormData.poste}
                    onChange={(e) => setEmployeeFormData({ ...employeeFormData, poste: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <CurrencyInput
                    label="Salaire"
                    value={employeeFormData.salaireCDF}
                    onChange={(value) => setEmployeeFormData({ ...employeeFormData, salaireCDF: value })}
                    min={0}
                    max={50000000} // 50M CDF (~17,857 USD) - realistic limit for employee salaries
                    step={1000}
                    exchangeRate={settings.tauxChangeUSDCDF}
                    required
                    showSlider={true}
                    allowUSDInput={true}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Date d'Embauche"
                    type="date"
                    value={employeeFormData.dateEmbauche}
                    onChange={(e) => setEmployeeFormData({ ...employeeFormData, dateEmbauche: e.target.value })}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Statut</InputLabel>
                    <Select
                      value={employeeFormData.statut}
                      label="Statut"
                      onChange={(e) => setEmployeeFormData({ ...employeeFormData, statut: e.target.value as any })}
                    >
                      <MenuItem value="actif">Actif</MenuItem>
                      <MenuItem value="inactif">Inactif</MenuItem>
                      <MenuItem value="suspendu">Suspendu</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Téléphone"
                    value={employeeFormData.telephone}
                    onChange={(e) => setEmployeeFormData({ ...employeeFormData, telephone: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Adresse"
                    value={employeeFormData.adresse}
                    onChange={(e) => setEmployeeFormData({ ...employeeFormData, adresse: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Notes"
                    multiline
                    rows={3}
                    value={employeeFormData.notes}
                    onChange={(e) => setEmployeeFormData({ ...employeeFormData, notes: e.target.value })}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseEmployeeDialog}>Annuler</Button>
              <Button
                onClick={handleSaveEmployee}
                variant="contained"
                disabled={loading || !employeeFormData.nomComplet.trim() || !employeeFormData.poste.trim()} // Updated validation to use nomComplet
                startIcon={loading ? <CircularProgress size={20} /> : <Person />}
              >
                {editingEmployee ? 'Modifier' : 'Ajouter'}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}
    </Box>
  );
};

export default EmployeePaymentsPage;
