import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>Field,
  Slider,
  Typography,
  InputAdornment,
  FormControl,
  FormLabel,
  ToggleButton,
  ToggleButtonGroup,
  Grid,
} from '@mui/material';
import { convertCDFToUSD, convertUSDToCDF, formatSingleCurrency } from '@/utils';
import { validateFinancialInput, ERROR_MESSAGES } from '@/utils/currencyUtils.js';

interface CurrencyInputProps {
  label: string;
  value: number; // Always in CDF (primary currency)
  onChange: (value: number) => void; // Always returns CDF value
  min?: number;
  max?: number;
  step?: number;
  exchangeRate: number; // USD to CDF rate
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  showSlider?: boolean;
  allowUSDInput?: boolean;
  onCurrencyModeChange?: (mode: 'CDF' | 'USD') => void; // Optional callback for currency mode changes
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  label,
  value,
  onChange,
  min = 0,
  max = 1000000000, // Increased to 1 billion CDF (~357,142 USD) to prevent spinner limitations
  step = 100,
  exchangeRate,
  disabled = false,
  required = false,
  error = false,
  helperText,
  showSlider = true,
  allowUSDInput = true,
  onCurrencyModeChange,
}) => {
  const [inputMode, setInputMode] = useState<'CDF' | 'USD'>('CDF');
  const [textValue, setTextValue] = useState<string>('');
  const [sliderValue, setSliderValue] = useState<number>(value);

  // Update local state when prop value changes
  useEffect(() => {
    setSliderValue(value);
    if (inputMode === 'CDF') {
      setTextValue(value.toString());
    } else {
      const usdValue = convertCDFToUSD(value, exchangeRate);
      setTextValue(usdValue.toFixed(2));
    }
  }, [value, inputMode, exchangeRate]);

  // Handle text input changes with standardized validation
  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    setTextValue(inputValue);

    const numericValue = parseFloat(inputValue) || 0;

    // Convert to CDF first for consistent validation
    let cdfValue: number;
    if (inputMode === 'CDF') {
      cdfValue = numericValue;
    } else {
      cdfValue = convertUSDToCDF(numericValue, exchangeRate);
    }

    // Validate the CDF value against CDF limits
    const validation = validateFinancialInput(cdfValue, 'Le montant', {
      allowZero: true,
      allowNegative: false,
      minValue: min,
      maxValue: max
    });

    if (validation.isValid) {
      // Ensure value is within bounds
      cdfValue = Math.max(min, Math.min(max, cdfValue));

      setSliderValue(cdfValue);
      onChange(cdfValue);
    }
  };

  // Handle slider changes
  const handleSliderChange = (_event: Event, newValue: number | number[]) => {
    const cdfValue = Array.isArray(newValue) ? newValue[0] : newValue;
    setSliderValue(cdfValue);
    
    if (inputMode === 'CDF') {
      setTextValue(cdfValue.toString());
    } else {
      const usdValue = convertCDFToUSD(cdfValue, exchangeRate);
      setTextValue(usdValue.toFixed(2));
    }
    
    onChange(cdfValue);
  };

  // Handle input mode toggle
  const handleModeChange = (_event: React.MouseEvent<HTMLElement>, newMode: 'CDF' | 'USD') => {
    if (newMode && newMode !== inputMode) {
      setInputMode(newMode);

      // Convert current text value to new mode
      if (newMode === 'USD') {
        const usdValue = convertCDFToUSD(sliderValue, exchangeRate);
        setTextValue(usdValue.toFixed(2));
      } else {
        setTextValue(sliderValue.toString());
      }

      // Notify parent component of currency mode change
      if (onCurrencyModeChange) {
        onCurrencyModeChange(newMode);
      }
    }
  };

  // Calculate display values
  const usdEquivalent = convertCDFToUSD(sliderValue, exchangeRate);
  const cdfEquivalent = sliderValue;

  return (
    <FormControl fullWidth disabled={disabled}>
      <FormLabel component="legend" sx={{ mb: 1 }}>
        {label} {required && '*'}
      </FormLabel>
      
      <Grid container spacing={2}>
        {/* Currency Mode Toggle */}
        {allowUSDInput && (
          <Grid item xs={12}>
            <ToggleButtonGroup
              value={inputMode}
              exclusive
              onChange={handleModeChange}
              size="small"
              disabled={disabled}
            >
              <ToggleButton value="CDF">
                Saisie en CDF
              </ToggleButton>
              <ToggleButton value="USD">
                Saisie en USD
              </ToggleButton>
            </ToggleButtonGroup>
          </Grid>
        )}

        {/* Text Input */}
        <Grid item xs={12} md={showSlider ? 6 : 12}>
          <TextField
            fullWidth
            label={`Montant (${inputMode})`}
            type="number"
            value={textValue}
            onChange={handleTextChange}
            disabled={disabled}
            error={error}
            helperText={helperText}
            inputProps={{
              // Remove HTML-level min/max to prevent browser spinner limitations
              // Our JavaScript validation handles the actual limits
              step: inputMode === 'CDF' ? step : 0.01,
              // Ensure large numbers are supported
              style: { MozAppearance: 'textfield' } // Firefox: hide spinner arrows if needed
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {inputMode === 'USD' ? '$' : ''}
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {inputMode === 'CDF' ? 'CDF' : 'USD'}
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        {/* Slider */}
        {showSlider && (
          <Grid item xs={12} md={6}>
            <Box sx={{ px: 2, pt: 3 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Curseur (CDF)
              </Typography>
              <Slider
                value={sliderValue}
                onChange={handleSliderChange}
                min={min}
                max={max}
                step={step}
                disabled={disabled}
                valueLabelDisplay="auto"
                valueLabelFormat={(value) => formatSingleCurrency(value, 'CDF')}
              />
            </Box>
          </Grid>
        )}

        {/* Currency Display */}
        <Grid item xs={12}>
          <Box sx={{ 
            p: 2, 
            bgcolor: 'grey.50', 
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'grey.300'
          }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Équivalences:
            </Typography>
            <Typography variant="body1" color="primary" fontWeight="medium">
              {formatSingleCurrency(cdfEquivalent, 'CDF')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ≈ {formatSingleCurrency(usdEquivalent, 'USD')}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </FormControl>
  );
};

export default CurrencyInput;
