import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  Card,
  CardContent,
  InputAdornment,
  Chip,
  Checkbox,
  FormControlLabel,
  CircularProgress,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  Receipt,
  TrendingDown,
  Category,
  DateRange,
  Download,
} from '@mui/icons-material';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';
import { ReceiptService } from '@/services/receipt';
import { CSVUtils, EXPENSE_COLUMNS } from '@/services/csv-utils';

// Components
import { CurrencyInput } from '@/components/CurrencyInput';
import { ReceiptPreviewModal } from '@/components/Receipt/ReceiptPreviewModal';

// Types
import { Expense } from '@/types';

// Utils
import { format, startOfMonth, endOfMonth, isWithinInterval, isValid } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatSingleCurrency, formatDualCurrencyForCard } from '@/utils';

// Helper function to safely format dates
const safeFormatDate = (dateValue: string | Date, formatString: string = 'dd/MM/yyyy'): string => {
  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    if (isValid(date)) {
      return format(date, formatString, { locale: fr });
    }
    return 'Date invalide';
  } catch (error) {
    console.warn('Invalid date value:', dateValue, error);
    return 'Date invalide';
  }
};

const ExpensesPage: React.FC = () => {
  // Temporary error handling to prevent app crash during logo testing
  try {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);
  const [formData, setFormData] = useState({
    description: '',
    montantCDF: 0,
    categorie: '',
    dateDepense: safeFormatDate(new Date(), 'yyyy-MM-dd'),
    notes: '',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [settings, setSettings] = useState<any>({ tauxChangeUSDCDF: 2800 });
  const [printReceipt, setPrintReceipt] = useState(false);

  // Receipt printing state
  const [receiptPreviewOpen, setReceiptPreviewOpen] = useState(false);
  const [receiptData, setReceiptData] = useState<any>(null);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);

  const permissions = adaptiveAuthService.getUserPermissions();
  const currentUser = adaptiveAuthService.getCurrentUser();

  // Expense categories
  const expenseCategories = [
    'Loyer',
    'Électricité',
    'Eau',
    'Internet',
    'Téléphone',
    'Transport',
    'Carburant',
    'Fournitures de bureau',
    'Marketing',
    'Maintenance',
    'Assurance',
    'Taxes',
    'Salaires',
    'Formation',
    'Équipement',
    'Autres',
  ];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterExpenses();
  }, [expenses, searchTerm, categoryFilter, dateFilter]);

  // Update rowsPerPage when filteredExpenses changes and "Voir tout" is selected
  useEffect(() => {
    if (rowsPerPage === -1) {
      // Force re-render when showing all items and data changes
      setRowsPerPage(filteredExpenses.length || 1);
    }
  }, [filteredExpenses.length, rowsPerPage]);

  const loadData = async () => {
    const expensesData = await adaptiveStorageService.getExpenses();
    const settingsData = await adaptiveStorageService.getSettings();
    setExpenses(expensesData);
    setSettings(settingsData);
  };

  const filterExpenses = () => {
    let filtered = expenses;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(expense =>
        expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expense.categorie.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (expense.notes && expense.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Category filter
    if (categoryFilter) {
      filtered = filtered.filter(expense => expense.categorie === categoryFilter);
    }

    // Date filter
    if (dateFilter) {
      const today = new Date();
      let startDate: Date;
      let endDate: Date;

      switch (dateFilter) {
        case 'today':
          startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
          break;
        case 'this_month':
          startDate = startOfMonth(today);
          endDate = endOfMonth(today);
          break;
        case 'last_month':
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          startDate = startOfMonth(lastMonth);
          endDate = endOfMonth(lastMonth);
          break;
        default:
          startDate = new Date(0);
          endDate = new Date();
      }

      filtered = filtered.filter(expense => {
        const expenseDate = new Date(expense.dateDepense);
        return isValid(expenseDate) && isWithinInterval(expenseDate, { start: startDate, end: endDate });
      });
    }

    setFilteredExpenses(filtered);
  };

  const handleOpenDialog = (expense?: Expense) => {
    if (expense) {
      setEditingExpense(expense);
      setFormData({
        description: expense.description,
        montantCDF: expense.montantCDF,
        categorie: expense.categorie,
        dateDepense: safeFormatDate(expense.dateDepense, 'yyyy-MM-dd'),
        notes: expense.notes || '',
      });
    } else {
      setEditingExpense(null);
      setFormData({
        description: '',
        montantCDF: 0,
        categorie: '',
        dateDepense: safeFormatDate(new Date(), 'yyyy-MM-dd'),
        notes: '',
      });
    }
    setOpenDialog(true);
    setError('');
    setSuccess('');
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingExpense(null);
    setError('');
    setSuccess('');
    setPrintReceipt(false);
  };

  const handleSaveExpense = async () => {
    // Validation
    if (!formData.description.trim()) {
      setError('La description est requise');
      return;
    }
    if (formData.montantCDF <= 0) {
      setError('Le montant doit être supérieur à 0');
      return;
    }
    if (!formData.categorie) {
      setError('La catégorie est requise');
      return;
    }
    if (!formData.dateDepense) {
      setError('La date est requise');
      return;
    }

    if (editingExpense) {
      // Update existing expense
      const updatedExpense: Expense = {
        ...editingExpense,
        description: formData.description.trim(),
        montantCDF: formData.montantCDF,
        montantUSD: formData.montantCDF / settings.tauxChangeUSDCDF,
        categorie: formData.categorie,
        dateDepense: formData.dateDepense,
        notes: formData.notes.trim() || undefined,
      };

      const updatedExpenses = expenses.map(e =>
        e.id === editingExpense.id ? updatedExpense : e
      );

      setExpenses(updatedExpenses);
      await adaptiveStorageService.setExpenses(updatedExpenses);
      setSuccess('Dépense mise à jour avec succès');
    } else {
      // Generate receipt number if printing is requested or auto-print is enabled
      const shouldPrint = printReceipt || settings.impression?.impressionAutomatique || false;
      const receiptNumber = shouldPrint ? await ReceiptService.generateExpenseReceiptNumber() : undefined;

      // Create new expense
      const newExpense: Expense = {
        id: Date.now().toString(),
        description: formData.description.trim(),
        montantCDF: formData.montantCDF,
        montantUSD: formData.montantCDF / settings.tauxChangeUSDCDF,
        categorie: formData.categorie,
        dateDepense: formData.dateDepense,
        notes: formData.notes.trim() || undefined,
        creePar: currentUser?.nom || 'Inconnu',
        numeroRecu: receiptNumber,
      };

      const updatedExpenses = [...expenses, newExpense];
      setExpenses(updatedExpenses);
      await adaptiveStorageService.setExpenses(updatedExpenses);
      setSuccess('Dépense créée avec succès');

      // Handle receipt printing if requested
      if (shouldPrint) {
        try {
          setIsGeneratingReceipt(true);
          const receiptData = await ReceiptService.createExpenseReceiptData(newExpense);
          setReceiptData(receiptData);
          setReceiptPreviewOpen(true);
        } catch (error) {
          console.error('Erreur lors de la génération du reçu:', error);
          setError('Erreur lors de la génération du reçu');
        } finally {
          setIsGeneratingReceipt(false);
        }
      }
    }

    setTimeout(() => {
      handleCloseDialog();
    }, 1500);
  };

  const handleDeleteExpense = async (expense: Expense) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer cette dépense "${expense.description}" ?`)) {
      const updatedExpenses = expenses.filter(e => e.id !== expense.id);
      setExpenses(updatedExpenses);
      await adaptiveStorageService.setExpenses(updatedExpenses);
      setSuccess('Dépense supprimée avec succès');
      setTimeout(() => setSuccess(''), 3000);
    }
  };



  const handleChangePage = (_: unknown, newPage: number) => {
    // Don't change page if showing all items
    if (rowsPerPage !== -1) {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setRowsPerPage(value);
    setPage(0); // Reset to first page when changing rows per page
  };

  // Stats
  const today = new Date();
  const thisMonth = {
    start: startOfMonth(today),
    end: endOfMonth(today),
  };

  const todayExpenses = expenses.filter(expense => {
    const expenseDate = new Date(expense.dateDepense);
    return isValid(expenseDate) && expenseDate.toDateString() === today.toDateString();
  });

  const thisMonthExpenses = expenses.filter(expense => {
    const expenseDate = new Date(expense.dateDepense);
    return isValid(expenseDate) && isWithinInterval(expenseDate, thisMonth);
  });

  const totalExpenses = expenses.length;
  const todayTotal = todayExpenses.reduce((sum, expense) => sum + expense.montantCDF, 0);
  const thisMonthTotal = thisMonthExpenses.reduce((sum, expense) => sum + expense.montantCDF, 0);
  const totalAmount = expenses.reduce((sum, expense) => sum + expense.montantCDF, 0);

  // Category breakdown
  const categoryBreakdown = expenses.reduce((acc: any, expense) => {
    acc[expense.categorie] = (acc[expense.categorie] || 0) + expense.montantUSD;
    return acc;
  }, {});

  const topCategories = Object.entries(categoryBreakdown)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 5);

  // Export function for expenses with Excel-compatible encoding
  const handleExportExpenses = () => {
    // Use the standardized EXPENSE_COLUMNS for consistent export format
    const filename = `SmartBoutique_Depenses_${safeFormatDate(new Date(), 'yyyy-MM-dd')}.csv`;
    CSVUtils.downloadCSV(filteredExpenses, EXPENSE_COLUMNS, filename);

    setSuccess('Dépenses exportées en CSV avec succès (compatible Excel)');
    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Gestion des Dépenses
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={handleExportExpenses}
          >
            Exporter les Dépenses
          </Button>
          {permissions.canManageExpenses && (
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog()}
            >
              Nouvelle Dépense
            </Button>
          )}
        </Box>
      </Box>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Dépenses du jour
                  </Typography>
                  <Typography variant="h6">{todayExpenses.length}</Typography>
                  <Typography variant="body2" color="error" fontWeight="medium">
                    {formatDualCurrencyForCard(todayTotal, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(todayTotal, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(todayTotal, settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <Receipt color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Dépenses du mois
                  </Typography>
                  <Typography variant="h6">{thisMonthExpenses.length}</Typography>
                  <Typography variant="body2" color="error" fontWeight="medium">
                    {formatDualCurrencyForCard(thisMonthTotal, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(thisMonthTotal, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(thisMonthTotal, settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <TrendingDown color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Dépenses
                  </Typography>
                  <Typography variant="h6">{totalExpenses}</Typography>
                  <Typography variant="body2" color="error" fontWeight="medium">
                    {formatDualCurrencyForCard(totalAmount, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(totalAmount, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(totalAmount, settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <Category color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Catégories
                  </Typography>
                  <Typography variant="h6">{Object.keys(categoryBreakdown).length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Top: {topCategories[0]?.[0] || 'N/A'}
                  </Typography>
                </Box>
                <DateRange color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Rechercher par description, catégorie ou notes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Catégorie</InputLabel>
              <Select
                value={categoryFilter}
                label="Catégorie"
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <MenuItem value="">Toutes les catégories</MenuItem>
                {expenseCategories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Période</InputLabel>
              <Select
                value={dateFilter}
                label="Période"
                onChange={(e) => setDateFilter(e.target.value)}
              >
                <MenuItem value="">Toutes les périodes</MenuItem>
                <MenuItem value="today">Aujourd'hui</MenuItem>
                <MenuItem value="this_month">Ce mois</MenuItem>
                <MenuItem value="last_month">Mois dernier</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Top Categories */}
      {topCategories.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Top 5 Catégories
          </Typography>
          <Grid container spacing={1}>
            {topCategories.map(([category, amount]) => (
              <Grid item key={category}>
                <Chip
                  label={`${category}: ${formatSingleCurrency(amount as number, 'CDF')}`}
                  color="primary"
                  variant="outlined"
                />
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}

      {/* Expenses Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Description</TableCell>
              <TableCell>Catégorie</TableCell>
              <TableCell align="right">Montant CDF</TableCell>
              <TableCell align="right">Montant USD</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Créé par</TableCell>
              {permissions.canManageExpenses && <TableCell align="center">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {(rowsPerPage === -1 ? filteredExpenses : filteredExpenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage))
              .map((expense) => (
                <TableRow
                  key={expense.id}
                  hover
                  onClick={() => handleOpenDialog(expense)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{expense.description}</Typography>
                      {expense.notes && (
                        <Typography variant="caption" color="text.secondary">
                          {expense.notes}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip label={expense.categorie} size="small" />
                  </TableCell>
                  <TableCell align="right">
                    {formatSingleCurrency(expense.montantCDF, 'CDF')}
                  </TableCell>
                  <TableCell align="right">
                    {expense.montantUSD ? formatSingleCurrency(expense.montantUSD, 'USD') : '-'}
                  </TableCell>
                  <TableCell>
                    {safeFormatDate(expense.dateDepense)}
                  </TableCell>
                  <TableCell>{expense.creePar}</TableCell>
                  {permissions.canManageExpenses && (
                    <TableCell align="center">
                      <Box display="flex" gap={1}>
                        <Tooltip title="Modifier">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(expense)}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        {adaptiveAuthService.hasRole(['super_admin', 'admin']) && (
                          <Tooltip title="Supprimer">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteExpense(expense)}
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  )}
                </TableRow>
              ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[
            5,
            10,
            25,
            50,
            100,
            { label: 'Voir tout', value: -1 }
          ]}
          component="div"
          count={filteredExpenses.length}
          rowsPerPage={rowsPerPage === -1 ? filteredExpenses.length : rowsPerPage}
          page={rowsPerPage === -1 ? 0 : page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) => {
            if (rowsPerPage === -1) {
              return `Affichage de tous les ${count} éléments`;
            }
            return `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`;
          }}
        />
      </TableContainer>

      {/* Expense Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingExpense ? 'Modifier la Dépense' : 'Nouvelle Dépense'}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description *"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <CurrencyInput
                label="Montant de la dépense"
                value={formData.montantCDF}
                onChange={(value) => setFormData({ ...formData, montantCDF: value })}
                min={0}
                max={100000000} // 100M CDF (~35,714 USD) - realistic limit for business expenses
                step={50}
                exchangeRate={settings.tauxChangeUSDCDF}
                required
                showSlider={true}
                allowUSDInput={true}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Catégorie *</InputLabel>
                <Select
                  value={formData.categorie}
                  label="Catégorie *"
                  onChange={(e) => setFormData({ ...formData, categorie: e.target.value })}
                >
                  {expenseCategories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Date de la dépense *"
                type="date"
                value={formData.dateDepense}
                onChange={(e) => setFormData({ ...formData, dateDepense: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          {!editingExpense && (
            <FormControlLabel
              control={
                <Checkbox
                  checked={printReceipt}
                  onChange={(e) => setPrintReceipt(e.target.checked)}
                />
              }
              label="Imprimer reçu"
              sx={{ mr: 'auto' }}
            />
          )}
          {isGeneratingReceipt && (
            <Box display="flex" alignItems="center" gap={1} mr={2}>
              <CircularProgress size={20} />
              <Typography variant="body2">Génération du reçu...</Typography>
            </Box>
          )}
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button
            onClick={handleSaveExpense}
            variant="contained"
            disabled={isGeneratingReceipt}
          >
            {editingExpense ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Receipt Preview Modal */}
      <ReceiptPreviewModal
        open={receiptPreviewOpen}
        onClose={() => setReceiptPreviewOpen(false)}
        receiptData={receiptData}
        onPrintSuccess={() => {
          setSuccess('Reçu imprimé avec succès');
          setTimeout(() => setSuccess(''), 3000);
        }}
      />
    </Box>
  );
  } catch (error) {
    console.error('ExpensesPage error:', error);
    return (
      <Box p={3}>
        <Typography variant="h4" gutterBottom>
          Dépenses
        </Typography>
        <Alert severity="error">
          Une erreur s'est produite lors du chargement de la page des dépenses.
          Veuillez recharger l'application ou contacter le support technique.
        </Alert>
      </Box>
    );
  }
};

export default ExpensesPage;
