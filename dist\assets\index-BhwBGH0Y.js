const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./web-B7UJ-r8L.js","./mui-C1SsAu0U.js","./vendor-B_Ch-B_d.js","./utils-Ch7HAeVX.js","./charts-UhR5A4U7.js","./web-B-I2arx7.js"])))=>i.map(i=>d[i]);
var e,t=Object.defineProperty,n=(e,n,r)=>((e,n,r)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[n]=r)(e,"symbol"!=typeof n?n+"":n,r);import{j as r,B as a,A as i,T as s,a as o,b as l,P as c,c as d,d as u,D as m,e as h,L as p,f as x,g,h as y,i as j,I as D,k as C,l as S,M as v,m as b,R as f,n as w,o as E,S as P,p as F,q as U,r as T,N as k,s as A,t as R,C as M,u as I,v as N,w as L,x as O,y as V,z as q,E as B,F as _,G as $,W as z,H as W,J as X,K as Q,O as J,Q as H,U as G,V as Y,X as K,Y as Z,Z as ee,_ as te,$ as ne,a0 as re,a1 as ae,a2 as ie,a3 as se,a4 as oe,a5 as le,a6 as ce,a7 as de,a8 as ue,a9 as me,aa as he,ab as pe,ac as xe,ad as ge,ae as ye,af as je,ag as De,ah as Ce,ai as Se,aj as ve,ak as be,al as fe,am as we,an as Ee,ao as Pe,ap as Fe,aq as Ue,ar as Te,as as ke,at as Ae,au as Re,av as Me,aw as Ie,ax as Ne,ay as Le,az as Oe,aA as Ve,aB as qe,aC as Be,aD as _e,aE as $e,aF as ze,aG as We,aH as Xe,aI as Qe,aJ as Je,aK as He,aL as Ge,aM as Ye,aN as Ke,aO as Ze,aP as et,aQ as tt,aR as nt,aS as rt,aT as at,aU as it,aV as st,aW as ot,aX as lt,aY as ct,aZ as dt,a_ as ut,a$ as mt,b0 as ht,b1 as pt,b2 as xt,b3 as gt,b4 as yt,b5 as jt,b6 as Dt,b7 as Ct}from"./mui-C1SsAu0U.js";import{c as St,r as vt,R as bt,a as ft}from"./vendor-B_Ch-B_d.js";import{b as wt,a as Et,c as Pt,d as Ft,f as Ut,s as Tt,e as kt,g as At,i as Rt,h as Mt,j as It,k as Nt,l as Lt,m as Ot,n as Vt,o as qt,p as Bt,q as _t}from"./utils-Ch7HAeVX.js";import{L as $t,D as zt,B as Wt,C as Xt,a as Qt,b as Jt,P as Ht,c as Gt,d as Yt,p as Kt,e as Zt,f as en,A as tn}from"./charts-UhR5A4U7.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var nn,rn,an={},sn=St;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function on(){return on=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},on.apply(this,arguments)}an.createRoot=sn.createRoot,an.hydrateRoot=sn.hydrateRoot,(rn=nn||(nn={})).Pop="POP",rn.Push="PUSH",rn.Replace="REPLACE";const ln="popstate";function cn(e){return void 0===e&&(e={}),function(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,s=a.history,o=nn.Pop,l=null,c=d();null==c&&(c=0,s.replaceState(on({},s.state,{idx:c}),""));function d(){return(s.state||{idx:null}).idx}function u(){o=nn.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:x.location,delta:t})}function m(e,t){o=nn.Push;let r=hn(x.location,e,t);n&&n(r,e),c=d()+1;let u=mn(r,c),m=x.createHref(r);try{s.pushState(u,"",m)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(m)}i&&l&&l({action:o,location:x.location,delta:1})}function h(e,t){o=nn.Replace;let r=hn(x.location,e,t);n&&n(r,e),c=d();let a=mn(r,c),u=x.createHref(r);s.replaceState(a,"",u),i&&l&&l({action:o,location:x.location,delta:0})}function p(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:pn(e);return n=n.replace(/ $/,"%20"),dn(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let x={get action(){return o},get location(){return e(a,s)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(ln,u),l=e,()=>{a.removeEventListener(ln,u),l=null}},createHref:e=>t(a,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:m,replace:h,go:e=>s.go(e)};return x}(function(e,t){let{pathname:n="/",search:r="",hash:a=""}=xn(e.location.hash.substr(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),hn("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let n=e.document.querySelector("base"),r="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");r=-1===n?t:t.slice(0,n)}return r+"#"+("string"==typeof t?t:pn(t))},function(e,t){un("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")},e)}function dn(e,t){if(!1===e||null==e)throw new Error(t)}function un(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function mn(e,t){return{usr:e.state,key:e.key,idx:t}}function hn(e,t,n,r){return void 0===n&&(n=null),on({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?xn(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function pn(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function xn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var gn,yn;function jn(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let r="string"==typeof t?xn(t):t,a=An(r.pathname||"/",n);if(null==a)return null;let i=Dn(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let s=null;for(let o=0;null==s&&o<i.length;++o){let e=kn(a);s=Un(i[o],e)}return s}(e,t,n)}function Dn(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,i)=>{let s={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};s.relativePath.startsWith("/")&&(dn(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(r.length));let o=Nn([r,s.relativePath]),l=n.concat(s);e.children&&e.children.length>0&&(dn(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),Dn(e.children,t,l,o)),(null!=e.path||e.index)&&t.push({path:o,score:Fn(o,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of Cn(e.path))a(e,t,r);else a(e,t)}),t}function Cn(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return a?[i,""]:[i];let s=Cn(r.join("/")),o=[];return o.push(...s.map(e=>""===e?i:[i,e].join("/"))),a&&o.push(...s),o.map(t=>e.startsWith("/")&&""===t?"/":t)}(yn=gn||(gn={})).data="data",yn.deferred="deferred",yn.redirect="redirect",yn.error="error";const Sn=/^:[\w-]+$/,vn=3,bn=2,fn=1,wn=10,En=-2,Pn=e=>"*"===e;function Fn(e,t){let n=e.split("/"),r=n.length;return n.some(Pn)&&(r+=En),t&&(r+=bn),n.filter(e=>!Pn(e)).reduce((e,t)=>e+(Sn.test(t)?vn:""===t?fn:wn),r)}function Un(e,t,n){let{routesMeta:r}=e,a={},i="/",s=[];for(let o=0;o<r.length;++o){let e=r[o],n=o===r.length-1,l="/"===i?t:t.slice(i.length)||"/",c=Tn({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},l),d=e.route;if(!c)return null;Object.assign(a,c.params),s.push({params:a,pathname:Nn([i,c.pathname]),pathnameBase:Ln(Nn([i,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(i=Nn([i,c.pathnameBase]))}return s}function Tn(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);un("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let i=new RegExp(a,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],s=i.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=o[n]||"";s=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:i,pathnameBase:s,pattern:e}}function kn(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return un(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function An(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function Rn(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Mn(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function In(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=xn(e):(a=on({},e),dn(!a.pathname||!a.pathname.includes("?"),Rn("?","pathname","search",a)),dn(!a.pathname||!a.pathname.includes("#"),Rn("#","pathname","hash",a)),dn(!a.search||!a.search.includes("#"),Rn("#","search","hash",a)));let i,s=""===e||""===a.pathname,o=s?"/":a.pathname;if(null==o)i=n;else{let e=t.length-1;if(!r&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}i=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?xn(e):e,i=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:i,search:On(r),hash:Vn(a)}}(a,i),c=o&&"/"!==o&&o.endsWith("/"),d=(s||"."===o)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const Nn=e=>e.join("/").replace(/\/\/+/g,"/"),Ln=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),On=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Vn=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const qn=["post","put","patch","delete"];new Set(qn);const Bn=["get",...qn];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function _n(){return _n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_n.apply(this,arguments)}new Set(Bn);const $n=vt.createContext(null),zn=vt.createContext(null),Wn=vt.createContext(null),Xn=vt.createContext(null),Qn=vt.createContext({outlet:null,matches:[],isDataRoute:!1}),Jn=vt.createContext(null);function Hn(){return null!=vt.useContext(Xn)}function Gn(){return Hn()||dn(!1),vt.useContext(Xn).location}function Yn(e){vt.useContext(Wn).static||vt.useLayoutEffect(e)}function Kn(){let{isDataRoute:e}=vt.useContext(Qn);return e?function(){let{router:e}=function(){let e=vt.useContext($n);return e||dn(!1),e}(ir.UseNavigateStable),t=or(sr.UseNavigateStable),n=vt.useRef(!1);return Yn(()=>{n.current=!0}),vt.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,_n({fromRouteId:t},a)))},[e,t])}():function(){Hn()||dn(!1);let e=vt.useContext($n),{basename:t,future:n,navigator:r}=vt.useContext(Wn),{matches:a}=vt.useContext(Qn),{pathname:i}=Gn(),s=JSON.stringify(Mn(a,n.v7_relativeSplatPath)),o=vt.useRef(!1);return Yn(()=>{o.current=!0}),vt.useCallback(function(n,a){if(void 0===a&&(a={}),!o.current)return;if("number"==typeof n)return void r.go(n);let l=In(n,JSON.parse(s),i,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:Nn([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)},[t,r,s,i,e])}()}const Zn=vt.createContext(null);function er(e,t){return function(e,t,n,r){Hn()||dn(!1);let{navigator:a}=vt.useContext(Wn),{matches:i}=vt.useContext(Qn),s=i[i.length-1],o=s?s.params:{};!s||s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let c,d=Gn();if(t){var u;let e="string"==typeof t?xn(t):t;"/"===l||(null==(u=e.pathname)?void 0:u.startsWith(l))||dn(!1),c=e}else c=d;let m=c.pathname||"/",h=m;if("/"!==l){let e=l.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=jn(e,{pathname:h}),x=function(e,t,n,r){var a;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===r&&(r=null);if(null==e){var i;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(i=r)&&i.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,o=null==(a=n)?void 0:a.errors;if(null!=o){let e=s.findIndex(e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id]));e>=0||dn(!1),s=s.slice(0,Math.min(s.length,e+1))}let l=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<s.length;d++){let e=s[d];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=d),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){l=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight((e,r,a)=>{let i,d=!1,u=null,m=null;var h;n&&(i=o&&r.route.id?o[r.route.id]:void 0,u=r.route.errorElement||nr,l&&(c<0&&0===a?(lr[h="route-fallback"]||(lr[h]=!0),d=!0,m=null):c===a&&(d=!0,m=r.route.hydrateFallbackElement||null)));let p=t.concat(s.slice(0,a+1)),x=()=>{let t;return t=i?u:d?m:r.route.Component?vt.createElement(r.route.Component,null):r.route.element?r.route.element:e,vt.createElement(ar,{match:r,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?vt.createElement(rr,{location:n.location,revalidation:n.revalidation,component:u,error:i,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()},null)}(p&&p.map(e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:Nn([l,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?l:Nn([l,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,r);if(t&&x)return vt.createElement(Xn.Provider,{value:{location:_n({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:nn.Pop}},x);return x}(e,t)}function tr(){let e=function(){var e;let t=vt.useContext(Jn),n=function(){let e=vt.useContext(zn);return e||dn(!1),e}(),r=or();if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return vt.createElement(vt.Fragment,null,vt.createElement("h2",null,"Unexpected Application Error!"),vt.createElement("h3",{style:{fontStyle:"italic"}},t),n?vt.createElement("pre",{style:r},n):null,null)}const nr=vt.createElement(tr,null);class rr extends vt.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?vt.createElement(Qn.Provider,{value:this.props.routeContext},vt.createElement(Jn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ar(e){let{routeContext:t,match:n,children:r}=e,a=vt.useContext($n);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),vt.createElement(Qn.Provider,{value:t},r)}var ir=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ir||{}),sr=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(sr||{});function or(e){let t=function(){let e=vt.useContext(Qn);return e||dn(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||dn(!1),n.route.id}const lr={};function cr(e){let{to:t,replace:n,state:r,relative:a}=e;Hn()||dn(!1);let{future:i,static:s}=vt.useContext(Wn),{matches:o}=vt.useContext(Qn),{pathname:l}=Gn(),c=Kn(),d=In(t,Mn(o,i.v7_relativeSplatPath),l,"path"===a),u=JSON.stringify(d);return vt.useEffect(()=>c(JSON.parse(u),{replace:n,state:r,relative:a}),[c,u,a,n,r]),null}function dr(e){return function(e){let t=vt.useContext(Qn).outlet;return t?vt.createElement(Zn.Provider,{value:e},t):t}(e.context)}function ur(e){dn(!1)}function mr(e){let{basename:t="/",children:n=null,location:r,navigationType:a=nn.Pop,navigator:i,static:s=!1,future:o}=e;Hn()&&dn(!1);let l=t.replace(/^\/*/,"/"),c=vt.useMemo(()=>({basename:l,navigator:i,static:s,future:_n({v7_relativeSplatPath:!1},o)}),[l,o,i,s]);"string"==typeof r&&(r=xn(r));let{pathname:d="/",search:u="",hash:m="",state:h=null,key:p="default"}=r,x=vt.useMemo(()=>{let e=An(d,l);return null==e?null:{location:{pathname:e,search:u,hash:m,state:h,key:p},navigationType:a}},[l,d,u,m,h,p,a]);return null==x?null:vt.createElement(Wn.Provider,{value:c},vt.createElement(Xn.Provider,{children:n,value:x}))}function hr(e){let{children:t,location:n}=e;return er(pr(t),n)}function pr(e,t){void 0===t&&(t=[]);let n=[];return vt.Children.forEach(e,(e,r)=>{if(!vt.isValidElement(e))return;let a=[...t,r];if(e.type===vt.Fragment)return void n.push.apply(n,pr(e.props.children,a));e.type!==ur&&dn(!1),e.props.index&&e.props.children&&dn(!1);let i={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=pr(e.props.children,a)),n.push(i)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise(()=>{});try{window.__reactRouterVersion="6"}catch(bi){}const xr=bt.startTransition;function gr(e){let{basename:t,children:n,future:r,window:a}=e,i=vt.useRef();null==i.current&&(i.current=cn({window:a,v5Compat:!0}));let s=i.current,[o,l]=vt.useState({action:s.action,location:s.location}),{v7_startTransition:c}=r||{},d=vt.useCallback(e=>{c&&xr?xr(()=>l(e)):l(e)},[l,c]);return vt.useLayoutEffect(()=>s.listen(d),[s,d]),vt.useEffect(()=>{return null==(e=r)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e},[r]),vt.createElement(mr,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:s,future:r})}var yr,jr,Dr,Cr;(jr=yr||(yr={})).UseScrollRestoration="useScrollRestoration",jr.UseSubmit="useSubmit",jr.UseSubmitFetcher="useSubmitFetcher",jr.UseFetcher="useFetcher",jr.useViewTransitionState="useViewTransitionState",(Cr=Dr||(Dr={})).UseFetcher="useFetcher",Cr.UseFetchers="useFetchers",Cr.UseScrollRestoration="useScrollRestoration";const Sr={components:{MuiBreadcrumbs:{defaultProps:{expandText:"Montrer le chemin"}},MuiTablePagination:{defaultProps:{getItemAriaLabel:e=>"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente",labelRowsPerPage:"Lignes par page :",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}–${t} sur ${-1!==n?n:`plus que ${t}`}`}},MuiRating:{defaultProps:{getLabelText:e=>`${e} Etoile${1!==e?"s":""}`,emptyLabelText:"Vide"}},MuiAutocomplete:{defaultProps:{clearText:"Vider",closeText:"Fermer",loadingText:"Chargement…",noOptionsText:"Pas de résultats",openText:"Ouvrir"}},MuiAlert:{defaultProps:{closeText:"Fermer"}},MuiPagination:{defaultProps:{"aria-label":"navigation de pagination",getItemAriaLabel:(e,t,n)=>"page"===e?`${n?"":"Aller à la "}page ${t}`:"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente"}}}},vr={},br=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),i=(null==a?void 0:a.nonce)||(null==a?void 0:a.getAttribute("nonce"));r=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,n),t in vr)return;vr[t]=!0;const r=t.endsWith(".css"),a=r?'[rel="stylesheet"]':"";if(!!n)for(let n=e.length-1;n>=0;n--){const a=e[n];if(a.href===t&&(!r||"stylesheet"===a.rel))return}else if(document.querySelector(`link[href="${t}"]${a}`))return;const s=document.createElement("link");return s.rel=r?"stylesheet":"modulepreload",r||(s.as="script"),s.crossOrigin="",s.href=t,i&&s.setAttribute("nonce",i),document.head.appendChild(s),r?new Promise((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then(t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)})};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var fr,wr;(wr=fr||(fr={})).Unimplemented="UNIMPLEMENTED",wr.Unavailable="UNAVAILABLE";class Er extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const Pr=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},r=n.Plugins=n.Plugins||{},a=()=>null!==t?t.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),i=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},s=new Map;return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=a,n.handleError=t=>e.console.error(t),n.isNativePlatform=()=>"web"!==a(),n.isPluginAvailable=e=>{const t=s.get(e);return!!(null==t?void 0:t.platforms.has(a()))||!!i(e)},n.registerPlugin=(e,o={})=>{const l=s.get(e);if(l)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),l.proxy;const c=a(),d=i(e);let u;const m=r=>{let a;const i=(...i)=>{const s=(async()=>(!u&&c in o?u=u="function"==typeof o[c]?await o[c]():o[c]:null!==t&&!u&&"web"in o&&(u=u="function"==typeof o.web?await o.web():o.web),u))().then(t=>{const s=((t,r)=>{var a,i;if(!d){if(t)return null===(i=t[r])||void 0===i?void 0:i.bind(t);throw new Er(`"${e}" plugin is not implemented on ${c}`,fr.Unimplemented)}{const i=null==d?void 0:d.methods.find(e=>r===e.name);if(i)return"promise"===i.rtype?t=>n.nativePromise(e,r.toString(),t):(t,a)=>n.nativeCallback(e,r.toString(),t,a);if(t)return null===(a=t[r])||void 0===a?void 0:a.bind(t)}})(t,r);if(s){const e=s(...i);return a=null==e?void 0:e.remove,e}throw new Er(`"${e}.${r}()" is not implemented on ${c}`,fr.Unimplemented)});return"addListener"===r&&(s.remove=async()=>a()),s};return i.toString=()=>`${r.toString()}() { [capacitor code] }`,Object.defineProperty(i,"name",{value:r,writable:!1,configurable:!1}),i},h=m("addListener"),p=m("removeListener"),x=(e,t)=>{const n=h({eventName:e},t),r=async()=>{const r=await n;p({eventName:e,callbackId:r},t)},a=new Promise(e=>n.then(()=>e({remove:r})));return a.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await r()},a},g=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?x:h;case"removeListener":return p;default:return m(t)}}});return r[e]=g,s.set(e,{name:e,proxy:g,platforms:new Set([...Object.keys(o),...d?[c]:[]])}),g},n.Exception=Er,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Fr=(e=>e.Capacitor=Pr(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),Ur=Fr.registerPlugin;class Tr{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const r=this.windowListeners[e];r&&!r.registered&&this.addWindowListener(r),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const r=this.listeners[e];if(r)r.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new Fr.Exception(e,fr.Unimplemented)}unavailable(e="not available"){return new Fr.Exception(e,fr.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const r=n.indexOf(t);this.listeners[e].splice(r,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const kr=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),Ar=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Rr extends Tr{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,r]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=Ar(n).trim(),r=Ar(r).trim(),t[n]=r}),t}async setCookie(e){try{const t=kr(e.key),n=kr(e.value),r=`; expires=${(e.expires||"").replace("expires=","")}`,a=(e.path||"/").replace("path=",""),i=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${r}; path=${a}; ${i};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}Ur("CapacitorCookies",{web:()=>new Rr});const Mr=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),r=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,r,a)=>(n[r]=e[t[a]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(r.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,r]of Object.entries(e.data||{}))t.set(n,r);n.body=t.toString()}else if(r.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const r=new Headers(n.headers);r.delete("content-type"),n.headers=r}else(r.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class Ir extends Tr{async request(e){const t=Mr(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[r,a]=n;let i,s;return Array.isArray(a)?(s="",a.forEach(e=>{i=t?encodeURIComponent(e):e,s+=`${r}=${i}&`}),s.slice(0,-1)):(i=t?encodeURIComponent(a):a,s=`${r}=${i}`),`${e}&${s}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),r=n?`${e.url}?${n}`:e.url,a=await fetch(r,t),i=a.headers.get("content-type")||"";let s,o,{responseType:l="text"}=a.ok?e:{};switch(i.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":o=await a.blob(),s=await(async e=>new Promise((t,n)=>{const r=new FileReader;r.onload=()=>{const e=r.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},r.onerror=e=>n(e),r.readAsDataURL(e)}))(o);break;case"json":s=await a.json();break;default:s=await a.text()}const c={};return a.headers.forEach((e,t)=>{c[t]=e}),{data:s,headers:c,status:a.status,url:a.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}Ur("CapacitorHttp",{web:()=>new Ir});const Nr=()=>{const e=Fr.getPlatform();return{isMobile:Fr.isNativePlatform(),isDesktop:!Fr.isNativePlatform(),isAndroid:"android"===e,isIOS:"ios"===e,isWeb:"web"===e,platform:e}},Lr=()=>Fr.isNativePlatform();class Or{static arrayToCSV(e,t,n=!0){if(!e||0===e.length){const e=t.map(e=>e.header).join(",")+"\n";return n?"\ufeff"+e:e}const r=[t.map(e=>e.header).join(","),...e.map(e=>t.map(t=>{let n=e[t.key];return null==n?n="":"date"===t.type&&n?n=new Date(n).toISOString().split("T")[0]:"boolean"===t.type?n=n?"Oui":"Non":"object"==typeof n&&(n=Or.formatComplexValue(n,t.key)),n=String(n),(n.includes(",")||n.includes('"')||n.includes("\n"))&&(n='"'+n.replace(/"/g,'""')+'"'),n}).join(","))].join("\n");return n?"\ufeff"+r:r}static formatComplexValue(e,t){if(!e)return"";if("paiements"===t&&Array.isArray(e))return 0===e.length?"Aucun paiement":e.map((e,t)=>{const n=[];if(n.push(`Paiement ${t+1}:`),e.montantCDF&&n.push(`${e.montantCDF.toLocaleString("fr-FR")} CDF`),e.montantUSD&&n.push(`${e.montantUSD.toLocaleString("fr-FR",{minimumFractionDigits:2})} USD`),e.methodePaiement){const t={cash:"Comptant",mobile_money:"Mobile Money",bank:"Banque",card:"Carte"};n.push(`via ${t[e.methodePaiement]||e.methodePaiement}`)}if(e.datePaiement){const t=new Date(e.datePaiement);n.push(`le ${t.toLocaleDateString("fr-FR")}`)}return e.notes&&n.push(`(${e.notes})`),n.join(" ")}).join(" | ");if("produits"===t&&Array.isArray(e))return 0===e.length?"Aucun produit":e.map((e,t)=>{const n=[];return e.nom&&n.push(`${e.nom}`),e.quantite&&n.push(`(Qté: ${e.quantite})`),e.prixUnitaireCDF&&n.push(`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF/unité`),n.join(" ")}).join(" | ");if(Array.isArray(e))return e.length>0?`${e.length} élément(s)`:"Aucun élément";if("object"==typeof e){const t=Object.keys(e);if(0===t.length)return"Objet vide";return t.slice(0,3).map(t=>{const n=e[t];return null!=n&&""!==n?`${t}: ${String(n).substring(0,20)}`:null}).filter(Boolean).join(", ")||"Données complexes"}return JSON.stringify(e)}static createExcelCompatibleBlob(e){return new Blob([e],{type:"text/csv;charset=utf-8"})}static downloadCSV(e,t,n){const r=Or.arrayToCSV(e,t,!0),a=Or.createExcelCompatibleBlob(r),i=URL.createObjectURL(a),s=document.createElement("a");s.href=i,s.download=n,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i)}static csvToArray(e,t){if(!e||""===e.trim())return[];const n=this.parseCSVLines(e);if(n.length<=1)return[];return n.slice(1).map((e,n)=>{const r=e,a={};return t.forEach((e,t)=>{let n=r[t]||"";"number"===e.type?n=""===n?0:parseFloat(n)||0:"boolean"===e.type?n="oui"===n.toLowerCase()||"true"===n.toLowerCase()||"1"===n:"date"===e.type&&n?n=new Date(n).toISOString():e.key.includes("prix")&&"string"==typeof n&&(n=parseFloat(n)||0),a[e.key]=n}),a.id||(a.id=String(n+1)),a})}static parseCSVLines(e){const t=[],n=e.split("\n");for(const r of n){if(""===r.trim())continue;const e=[];let n="",a=!1,i=0;for(;i<r.length;){const t=r[i];'"'===t?a&&'"'===r[i+1]?(n+='"',i+=2):(a=!a,i++):","!==t||a?(n+=t,i++):(e.push(n),n="",i++)}e.push(n),t.push(e)}return t}static validateCSVData(e,t){const n=[];return e.forEach((e,r)=>{t.forEach(t=>{!t.required||void 0!==e[t.key]&&null!==e[t.key]&&""!==e[t.key]||n.push(`Ligne ${r+2}: Le champ "${t.header}" est requis`),"number"===t.type&&void 0!==e[t.key]&&isNaN(Number(e[t.key]))&&n.push(`Ligne ${r+2}: Le champ "${t.header}" doit être un nombre`)})}),{isValid:0===n.length,errors:n}}static generateTemplate(e){const t=[{}];return e.forEach(e=>{switch(e.type){case"string":t[0][e.key]="Exemple";break;case"number":t[0][e.key]=100;break;case"boolean":t[0][e.key]=!0;break;case"date":t[0][e.key]=(new Date).toISOString()}}),this.arrayToCSV(t,e)}}const Vr=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom du Produit",type:"string",required:!0},{key:"description",header:"Description",type:"string"},{key:"prixAchatCDF",header:"Prix d'achat CDF",type:"number",required:!0},{key:"prixAchatUSD",header:"Prix d'achat USD",type:"number"},{key:"prixCDF",header:"Prix de vente CDF",type:"number",required:!0},{key:"prixUSD",header:"Prix de vente USD",type:"number"},{key:"beneficeUnitaireCDF",header:"Bénéfice unitaire CDF",type:"number"},{key:"beneficeUnitaireUSD",header:"Bénéfice unitaire USD",type:"number"},{key:"codeQR",header:"Code QR",type:"string"},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"stock",header:"Stock",type:"number",required:!0},{key:"stockMin",header:"Stock Minimum",type:"number"},{key:"codeBarres",header:"Code Barres",type:"string"},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"dateModification",header:"Date de Modification",type:"date"}],qr=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom",type:"string",required:!0},{key:"email",header:"Email",type:"string",required:!0},{key:"role",header:"Rôle",type:"string",required:!0},{key:"motDePasse",header:"Mot de Passe",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"actif",header:"Actif",type:"boolean"}],Br=[{key:"id",header:"ID",type:"string",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"client",header:"Client",type:"string"},{key:"produits",header:"Produits (JSON)",type:"string",required:!0},{key:"totalCDF",header:"Total CDF",type:"number",required:!0},{key:"totalUSD",header:"Total USD",type:"number",required:!0},{key:"typePaiement",header:"Type de Paiement",type:"string",required:!0},{key:"typeVente",header:"Type de Vente",type:"string",required:!0},{key:"vendeur",header:"Vendeur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],_r=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomClient",header:"Client",type:"string",required:!0},{key:"telephoneClient",header:"Téléphone",type:"string"},{key:"adresseClient",header:"Adresse",type:"string"},{key:"montantTotalCDF",header:"Montant Total CDF",type:"number",required:!0},{key:"montantTotalUSD",header:"Montant Total USD",type:"number"},{key:"montantPayeCDF",header:"Montant Payé CDF",type:"number",required:!0},{key:"montantPayeUSD",header:"Montant Payé USD",type:"number"},{key:"montantRestantCDF",header:"Montant Restant CDF",type:"number",required:!0},{key:"montantRestantUSD",header:"Montant Restant USD",type:"number"},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateEcheance",header:"Date d'Échéance",type:"date"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"statutPaiement",header:"Statut de Paiement",type:"string",required:!0},{key:"venteId",header:"ID de Vente",type:"string"},{key:"paiements",header:"Paiements",type:"string"},{key:"notes",header:"Notes",type:"string"}],$r=[{key:"id",header:"ID",type:"string",required:!0},{key:"description",header:"Description",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"utilisateur",header:"Utilisateur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],zr=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomComplet",header:"Nom Complet",type:"string",required:!0},{key:"poste",header:"Poste",type:"string",required:!0},{key:"salaireCDF",header:"Salaire CDF",type:"number",required:!0},{key:"salaireUSD",header:"Salaire USD",type:"number"},{key:"dateEmbauche",header:"Date d'Embauche",type:"date",required:!0},{key:"telephone",header:"Téléphone",type:"string"},{key:"adresse",header:"Adresse",type:"string"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Wr=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomEmploye",header:"Nom Employé",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number"},{key:"datePaiement",header:"Date de Paiement",type:"date",required:!0},{key:"methodePaiement",header:"Méthode de Paiement",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Xr=[{key:"cle",header:"Clé",type:"string",required:!0},{key:"valeur",header:"Valeur",type:"string",required:!0},{key:"type",header:"Type",type:"string",required:!0},{key:"description",header:"Description",type:"string"}];function Qr(e){var t,n;const r=[];return r.push({cle:"tauxChangeUSDCDF",valeur:(null==(t=e.tauxChangeUSDCDF)?void 0:t.toString())||"2800",type:"number",description:"Taux de change USD vers CDF"}),r.push({cle:"seuilStockBas",valeur:(null==(n=e.seuilStockBas)?void 0:n.toString())||"10",type:"number",description:"Seuil de stock bas"}),e.categories&&Array.isArray(e.categories)&&r.push({cle:"categories",valeur:JSON.stringify(e.categories),type:"json",description:"Catégories de produits"}),e.entreprise&&r.push({cle:"entreprise",valeur:JSON.stringify(e.entreprise),type:"json",description:"Informations de l'entreprise"}),e.impression&&r.push({cle:"impression",valeur:JSON.stringify(e.impression),type:"json",description:"Paramètres d'impression des reçus"}),r}const Jr=new class{constructor(){n(this,"prefix","smartboutique_csv_")}getKey(e){return`${this.prefix}${e}`}setCSV(e,t,n){try{const r=Or.arrayToCSV(t,n);localStorage.setItem(this.getKey(e),r)}catch(r){console.error(`Erreur lors de la sauvegarde CSV ${e}:`,r)}}getCSV(e,t,n=[]){try{const r=localStorage.getItem(this.getKey(e));return r?Or.csvToArray(r,t):n}catch(r){return console.error(`Erreur lors de la lecture CSV ${e}:`,r),n}}set(e,t){try{const n=JSON.stringify(t);localStorage.setItem(this.getKey(e),n)}catch(n){console.error("Erreur lors de la sauvegarde:",n)}}get(e,t){try{const n=localStorage.getItem(this.getKey(e));return null===n?t:JSON.parse(n)}catch(n){return console.error("Erreur lors de la lecture:",n),t}}remove(e){localStorage.removeItem(this.getKey(e))}clear(){Object.keys(localStorage).forEach(e=>{e.startsWith(this.prefix)&&localStorage.removeItem(e)})}getUsers(){return this.getCSV("users",qr,[])}setUsers(e){this.setCSV("users",e,qr)}getProducts(){return this.getCSV("products",Vr,[]).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}setProducts(e){this.setCSV("products",e,Vr)}getSales(){try{const e=this.getCSV("sales",Br,[]);return e.filter(e=>e&&"object"==typeof e).map(e=>{const t={...e,datevente:e.date||e.datevente||(new Date).toISOString(),methodePaiement:e.typePaiement||e.methodePaiement||"cash",nomClient:e.client||e.nomClient||"Client"};t.totalCDF=Number(t.totalCDF)||0,t.totalUSD=t.totalUSD?Number(t.totalUSD):void 0;let n=t.produits||[];if("string"==typeof n)try{n=JSON.parse(n)}catch(r){console.warn("Error parsing produits JSON:",r),n=[]}return Array.isArray(n)?t.produits=n.map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})):t.produits=[],t})}catch(e){return console.error("Error getting sales data:",e),[]}}setSales(e){const t=e.map(e=>({...e,date:e.datevente||e.date,typePaiement:e.methodePaiement||e.typePaiement,client:e.nomClient||e.client,produits:"string"==typeof e.produits?e.produits:JSON.stringify(e.produits||[])}));this.setCSV("sales",t,Br)}getDebts(){return this.getCSV("debts",_r,[]).map(e=>{e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0;let t=e.paiements||[];if("string"==typeof t)try{t=JSON.parse(t)}catch(n){console.warn("Error parsing paiements JSON:",n),t=[]}return Array.isArray(t)?e.paiements=t.map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})):e.paiements=[],e})}setDebts(e){this.setCSV("debts",e,_r)}getDettes(){return this.getDebts()}setDettes(e){this.setDebts(e)}getCreances(){return this.getDebts()}setCreances(e){this.setDebts(e)}getExpenses(){return this.getCSV("expenses",$r,[])}setExpenses(e){this.setCSV("expenses",e,$r)}getEmployeePayments(){return this.getCSV("employee_payments",Wr,[])}setEmployeePayments(e){this.setCSV("employee_payments",e,Wr)}addEmployeePayment(e){const t=this.getEmployeePayments();t.push(e),this.setEmployeePayments(t)}updateEmployeePayment(e){const t=this.getEmployeePayments(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployeePayments(t))}deleteEmployeePayment(e){const t=this.getEmployeePayments().filter(t=>t.id!==e);this.setEmployeePayments(t)}getEmployees(){return this.getCSV("employees",zr,[])}setEmployees(e){this.setCSV("employees",e,zr)}addEmployee(e){const t=this.getEmployees();t.push(e),this.setEmployees(t)}updateEmployee(e){const t=this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployees(t))}deleteEmployee(e){const t=this.getEmployees().filter(t=>t.id!==e);this.setEmployees(t)}getSettings(){return this.get("settings",{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}})}setSettings(e){this.set("settings",e)}getCurrentUser(){return this.get("currentUser",null)}setCurrentUser(e){this.set("currentUser",e)}initializeDefaultData(){if(0===this.getUsers().length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];this.setUsers(e)}0===this.getProducts().length&&this.initializeProductCatalog();0===this.getSales().length&&this.initializeSampleSales();0===this.getDebts().length&&this.initializeSampleDebts()}clearAllData(){localStorage.clear(),console.log("All localStorage data cleared")}initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixAchatCDF:168e4,prixAchatUSD:600,prixCDF:224e4,prixUSD:800,beneficeUnitaireCDF:56e4,beneficeUnitaireUSD:200,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixAchatCDF:7e4,prixAchatUSD:25,prixCDF:98e3,prixUSD:35,beneficeUnitaireCDF:28e3,beneficeUnitaireUSD:10,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixAchatCDF:25200,prixAchatUSD:9,prixCDF:33600,prixUSD:12,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixAchatCDF:6300,prixAchatUSD:2.25,prixCDF:8400,prixUSD:3,beneficeUnitaireCDF:2100,beneficeUnitaireUSD:.75,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixAchatCDF:33600,prixAchatUSD:12,prixCDF:42e3,prixUSD:15,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];this.setProducts(e)}initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),r=[{id:"1",datevente:t.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant"},{id:"2",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70},{produitId:"4",nomProduit:"Sucre",quantite:3,prixUnitaireCDF:8400,prixUnitaireUSD:3,totalCDF:25200,totalUSD:9}],totalCDF:221200,totalUSD:79,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement mobile money"},{id:"3",datevente:n.toISOString(),nomClient:"Paul Tshisekedi",telephoneClient:"+243 900 000 003",produits:[{produitId:"5",nomProduit:"Riz",quantite:2,prixUnitaireCDF:42e3,prixUnitaireUSD:15,totalCDF:84e3,totalUSD:30}],totalCDF:84e3,totalUSD:30,methodePaiement:"card",typeVente:"cash",vendeur:"Employé",notes:"Paiement par carte"}];this.setSales(r)}initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),r=new Date(e.getTime()+2592e6),a=new Date(e.getTime()+12096e5),i=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:r.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:a.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:r.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];this.setDebts(i)}exportData(){const e=this.getUsers(),t=this.getProducts(),n=this.getSales(),r=this.getDebts(),a=this.getExpenses(),i=this.getEmployeePayments(),s=this.getSettings(),o={exportDate:(new Date).toISOString(),products:Or.arrayToCSV(t,Vr),users:Or.arrayToCSV(e,qr),sales:Or.arrayToCSV(n,Br),debts:Or.arrayToCSV(r,_r),expenses:Or.arrayToCSV(a,$r),employeePayments:Or.arrayToCSV(i,Wr),settings:Or.arrayToCSV(Qr(s),Xr)};return{csvData:`SmartBoutique - Sauvegarde Complète (Desktop)\nDate d'exportation: ${o.exportDate}\n\n=== PRODUITS ===\n${o.products}\n\n=== UTILISATEURS ===\n${o.users}\n\n=== VENTES ===\n${o.sales}\n\n=== DETTES ===\n${o.debts}\n\n=== DÉPENSES ===\n${o.expenses}\n\n=== PAIEMENTS EMPLOYÉS ===\n${o.employeePayments}\n\n=== PARAMÈTRES ===\n${o.settings}\n`,exportDate:o.exportDate}}importData(e){try{return e.csvData&&"string"==typeof e.csvData?this.importFromCSVBackup(e.csvData):(e.users&&this.setUsers(e.users),e.products&&this.setProducts(e.products),e.sales&&this.setSales(e.sales),e.debts&&this.setDebts(e.debts),e.expenses&&this.setExpenses(e.expenses),e.employeePayments&&this.setEmployeePayments(e.employeePayments),e.settings&&this.setSettings(e.settings),!0)}catch(t){return console.error("Erreur lors de l'importation:",t),!1}}importFromCSVBackup(e){try{const t=this.parseCSVBackup(e);if(t.products){const e=Or.csvToArray(t.products,Vr);this.setProducts(e)}if(t.users){const e=Or.csvToArray(t.users,qr);this.setUsers(e)}if(t.sales){const e=Or.csvToArray(t.sales,Br);this.setSales(e)}if(t.debts){const e=Or.csvToArray(t.debts,_r);this.setDebts(e)}if(t.expenses){const e=Or.csvToArray(t.expenses,$r);this.setExpenses(e)}if(t.employeePayments){const e=Or.csvToArray(t.employeePayments,Wr);this.setEmployeePayments(e)}if(t.settings){const e=function(e){const t={};return e.forEach(e=>{const n=e.cle;let r=e.valeur;switch(e.type){case"number":r=parseFloat(r)||0;break;case"boolean":r="true"===r||"1"===r||"Oui"===r;break;case"json":try{r=JSON.parse(r)}catch(a){console.error(`Erreur lors du parsing JSON pour ${n}:`,a),r=null}}t[n]=r}),t}(Or.csvToArray(t.settings,Xr));this.setSettings(e)}return!0}catch(t){return console.error("Erreur lors de l'importation CSV:",t),!1}}parseCSVBackup(e){const t={},n=e.split("\n");let r="",a=[];for(const i of n)if(i.startsWith("=== ")&&i.endsWith(" ===")){r&&a.length>0&&(t[r]=a.join("\n"));switch(i.replace(/=== | ===/g,"").toLowerCase()){case"produits":r="products";break;case"utilisateurs":r="users";break;case"ventes":r="sales";break;case"dettes":r="debts";break;case"dépenses":r="expenses";break;case"paiements employés":r="employeePayments";break;case"paramètres":r="settings";break;default:r=""}a=[]}else r&&""!==i.trim()&&a.push(i);return r&&a.length>0&&(t[r]=a.join("\n")),t}exportCSV(e){let t=[],n=[];switch(e){case"products":t=this.getProducts(),n=Vr;break;case"users":t=this.getUsers(),n=qr;break;case"sales":t=this.getSales(),n=Br;break;case"debts":t=this.getDebts(),n=_r;break;case"expenses":t=this.getExpenses(),n=$r;break;case"employee_payments":t=this.getEmployeePayments(),n=Wr}return Or.arrayToCSV(t,n)}importCSV(e,t,n=!1){try{let r=[],a=[];switch(e){case"products":r=Vr,a=n?[]:this.getProducts();break;case"users":r=qr,a=n?[]:this.getUsers();break;default:return{success:!1,message:"Type de données non supporté",errors:[]}}const i=Or.csvToArray(t,r),s=Or.validateCSVData(i,r);if(!s.isValid)return{success:!1,message:"Données invalides",errors:s.errors};let o=i;if(!n&&a.length>0){const e=new Set(a.map(e=>e.id)),t=i.filter(t=>!e.has(t.id));o=[...a,...t]}switch(e){case"products":this.setProducts(o);break;case"users":this.setUsers(o)}return{success:!0,message:`${i.length} éléments importés avec succès`,errors:[]}}catch(r){return{success:!1,message:"Erreur lors de l'importation: "+r.message,errors:[r.message]}}}},Hr=Ur("Preferences",{web:()=>br(()=>import("./web-B7UJ-r8L.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(e=>new e.PreferencesWeb)}),Gr=Object.freeze(Object.defineProperty({__proto__:null,Preferences:Hr},Symbol.toStringTag,{value:"Module"}));const Yr=new class{constructor(){n(this,"CSV_PREFIX","smartboutique_csv_")}async setCSV(e,t,n){try{const r=Or.arrayToCSV(t,n);await Hr.set({key:this.CSV_PREFIX+e,value:r})}catch(r){throw console.error(`Erreur lors de la sauvegarde CSV ${e}:`,r),r}}async getCSV(e,t,n=[]){try{const r=await Hr.get({key:this.CSV_PREFIX+e});return r.value?Or.csvToArray(r.value,t):n}catch(r){return console.error(`Erreur lors de la lecture CSV ${e}:`,r),n}}async getProducts(){return(await this.getCSV("products",Vr,[])).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}async setProducts(e){await this.setCSV("products",e,Vr)}async getUsers(){return this.getCSV("users",qr,[])}async setUsers(e){await this.setCSV("users",e,qr)}async getSales(){return(await this.getCSV("sales",Br,[])).map(e=>(e.totalCDF=Number(e.totalCDF)||0,e.totalUSD=e.totalUSD?Number(e.totalUSD):void 0,e.produits=(e.produits||[]).map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})),e))}async setSales(e){await this.setCSV("sales",e,Br)}async getDebts(){return(await this.getCSV("debts",_r,[])).map(e=>(e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0,e.paiements=(e.paiements||[]).map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})),e))}async setDebts(e){await this.setCSV("debts",e,_r)}async getExpenses(){return this.getCSV("expenses",$r,[])}async setExpenses(e){await this.setCSV("expenses",e,$r)}async getEmployees(){return this.getCSV("employees",zr,[])}async setEmployees(e){await this.setCSV("employees",e,zr)}async addEmployee(e){const t=await this.getEmployees();t.push(e),await this.setEmployees(t)}async updateEmployee(e){const t=await this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,await this.setEmployees(t))}async deleteEmployee(e){const t=(await this.getEmployees()).filter(t=>t.id!==e);await this.setEmployees(t)}async getSettings(){const e=await Hr.get({key:this.CSV_PREFIX+"settings"});if(e.value)try{return JSON.parse(e.value)}catch(t){console.error("Erreur lors du parsing des paramètres:",t)}return{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}}}async setSettings(e){await Hr.set({key:this.CSV_PREFIX+"settings",value:JSON.stringify(e)})}async getCurrentUser(){const e=await Hr.get({key:this.CSV_PREFIX+"currentUser"});return e.value?JSON.parse(e.value):null}async setCurrentUser(e){await Hr.set({key:this.CSV_PREFIX+"currentUser",value:JSON.stringify(e)})}async initializeDefaultData(){if(0===(await this.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await this.setUsers(e)}0===(await this.getProducts()).length&&await this.initializeProductCatalog();0===(await this.getDebts()).length&&await this.initializeSampleDebts()}async initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixCDF:224e4,prixUSD:800,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixCDF:98e3,prixUSD:35,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixCDF:33600,prixUSD:12,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixCDF:8400,prixUSD:3,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixCDF:42e3,prixUSD:15,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"11",nom:"Huile de Palme",description:"Huile de palme rouge 1 litre",prixCDF:16800,prixUSD:6,codeQR:"SB12345688OPQR",categorie:"Alimentation",stock:40,stockMin:8,codeBarres:"1234567890133",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"12",nom:"Farine de Maïs",description:"Farine de maïs blanche 2kg",prixCDF:11200,prixUSD:4,codeQR:"SB12345689STUV",categorie:"Alimentation",stock:75,stockMin:15,codeBarres:"1234567890134",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];await this.setProducts(e)}async clearAllData(){try{const e=["products","users","sales","debts","expenses","settings","currentUser"];for(const t of e)await Hr.remove({key:this.CSV_PREFIX+t});console.log("✅ All CSV data cleared from mobile storage")}catch(e){throw console.error("❌ Error clearing CSV data:",e),e}}async initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),r=new Date(e.getTime()+2592e6),a=new Date(e.getTime()+12096e5),i=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:r.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:a.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:r.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];await this.setDebts(i)}};let Kr=null,Zr=null,ea=null;const ta="undefined"==typeof window&&"undefined"!=typeof process&&(null==(e=process.versions)?void 0:e.electron);if(ta)try{Kr=require("better-sqlite3"),Zr=require("electron").app,ea=require("path").join}catch(fi){console.warn("better-sqlite3 not available:",fi)}class na{constructor(){if(n(this,"db"),n(this,"dbPath"),n(this,"isAvailable",!1),ta&&Kr)try{const e=(null==Zr?void 0:Zr.getPath("userData"))||"./data";this.dbPath=ea(e,"smartboutique.db"),this.initializeDatabase(),this.isAvailable=!0}catch(fi){console.error("Failed to initialize SQLite:",fi)}else console.warn("SQLite not available in this context")}initializeDatabase(){if(!Kr)throw new Error("better-sqlite3 not available");this.db=new Kr(this.dbPath),this.db.pragma("journal_mode = WAL"),this.db.pragma("synchronous = NORMAL"),this.db.pragma("cache_size = 1000"),this.db.pragma("temp_store = memory"),this.createTables(),this.createIndexes()}checkAvailability(){if(!this.isAvailable)throw new Error("SQLite storage not available in this context")}createTables(){this.db.exec("\n      CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employees (\n        id TEXT PRIMARY KEY,\n        nomComplet TEXT NOT NULL,\n        poste TEXT NOT NULL,\n        salaireCDF REAL NOT NULL,\n        salaireUSD REAL,\n        dateEmbauche TEXT NOT NULL,\n        telephone TEXT,\n        adresse TEXT,\n        statut TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employee_payments (\n        id TEXT PRIMARY KEY,\n        nomEmploye TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        datePaiement TEXT NOT NULL,\n        methodePaiement TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    ")}createIndexes(){this.db.exec("\n      CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);\n      CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);\n      CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);\n      CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);\n      CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);\n      CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);\n      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);\n      CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_date ON employee_payments(datePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_employe ON employee_payments(nomEmploye);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_methode ON employee_payments(methodePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_cree_par ON employee_payments(creePar);\n    ")}getProducts(){this.checkAvailability();return this.db.prepare("SELECT * FROM products ORDER BY nom").all()}getProduct(e){this.checkAvailability();return this.db.prepare("SELECT * FROM products WHERE id = ?").get(e)}setProducts(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM products").run();const t=this.db.prepare("\n        INSERT INTO products (\n          id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n          beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n          stockMin, codeBarres, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.description,n.prixAchatCDF,n.prixAchatUSD,n.prixCDF,n.prixUSD,n.beneficeUnitaireCDF,n.beneficeUnitaireUSD,n.codeQR,n.categorie,n.stock,n.stockMin,n.codeBarres,n.dateCreation,n.dateModification)})(e)}addProduct(e){this.db.prepare("\n      INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification, quantiteEnStock,\n        coutAchatStockCDF, coutAchatStockUSD, prixParPieceCDF, prixParPieceUSD\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification,e.quantiteEnStock,e.coutAchatStockCDF,e.coutAchatStockUSD,e.prixParPieceCDF,e.prixParPieceUSD)}updateProduct(e){this.db.prepare("\n      UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id)}deleteProduct(e){this.db.prepare("DELETE FROM products WHERE id = ?").run(e)}getUsers(){return this.db.prepare("SELECT * FROM users ORDER BY nom").all().map(e=>({...e,actif:Boolean(e.actif)}))}setUsers(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM users").run();const t=this.db.prepare("\n        INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.email,n.role,n.motDePasse,n.dateCreation,n.actif?1:0)})(e)}getSales(){return this.db.prepare("SELECT * FROM sales ORDER BY date DESC").all().map(e=>({...e,produits:JSON.parse(e.produits)}))}setSales(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM sales").run();const t=this.db.prepare("\n        INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu)\n        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.datevente,n.nomClient,JSON.stringify(n.produits),n.totalCDF,n.totalUSD,n.methodePaiement,n.typeVente,n.vendeur,n.numeroRecu)})(e)}getEmployeePayments(){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments ORDER BY datePaiement DESC").all()}getEmployeePayment(e){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments WHERE id = ?").get(e)}addEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employee_payments (\n        id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n        notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.creePar,e.dateCreation,e.dateModification)}clearAllData(){this.checkAvailability();try{this.db.transaction(()=>{this.db.prepare("DELETE FROM products").run(),this.db.prepare("DELETE FROM users").run(),this.db.prepare("DELETE FROM sales").run(),this.db.prepare("DELETE FROM debts").run(),this.db.prepare("DELETE FROM expenses").run(),this.db.prepare("DELETE FROM employee_payments").run(),this.db.prepare("DELETE FROM settings").run(),this.db.prepare("DELETE FROM sqlite_sequence").run()})(),console.log("✅ All SQLite data cleared successfully")}catch(fi){throw console.error("❌ Error clearing SQLite data:",fi),fi}}updateEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      UPDATE employee_payments SET\n        nomEmploye = ?, montantCDF = ?, montantUSD = ?, datePaiement = ?,\n        methodePaiement = ?, notes = ?, dateModification = ?\n      WHERE id = ?\n    ").run(e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.dateModification,e.id)}deleteEmployeePayment(e){this.checkAvailability();this.db.prepare("DELETE FROM employee_payments WHERE id = ?").run(e)}setEmployeePayments(e){this.checkAvailability();this.db.transaction(e=>{this.db.prepare("DELETE FROM employee_payments").run();const t=this.db.prepare("\n        INSERT INTO employee_payments (\n          id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n          notes, creePar, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nomEmploye,n.montantCDF,n.montantUSD,n.datePaiement,n.methodePaiement,n.notes,n.creePar,n.dateCreation,n.dateModification)})(e)}migrateFromCSV(e){console.log("Starting migration from CSV to SQLite...");this.db.transaction(()=>{var t,n,r;(null==(t=e.products)?void 0:t.length)&&this.setProducts(e.products),(null==(n=e.users)?void 0:n.length)&&this.setUsers(e.users),(null==(r=e.sales)?void 0:r.length)&&this.setSales(e.sales)})(),console.log("Migration completed successfully")}exportToCSV(){return{products:"",users:"",sales:""}}close(){this.db.close()}getStats(){const e=this.db.prepare("SELECT COUNT(*) as count FROM products").get(),t=this.db.prepare("SELECT COUNT(*) as count FROM users").get(),n=this.db.prepare("SELECT COUNT(*) as count FROM sales").get(),r=this.db.prepare("SELECT COUNT(*) as count FROM employee_payments").get();return{products:e.count,users:t.count,sales:n.count,employeePayments:r.count,dbSize:0}}getEmployees(){this.checkAvailability();return this.db.prepare("SELECT * FROM employees ORDER BY nom ASC").all()}addEmployee(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employees (\n        id, nomComplet, poste, salaireCDF, salaireUSD, dateEmbauche,\n        telephone, adresse, statut, notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.creePar,e.dateCreation,e.dateModification)}updateEmployee(e){this.checkAvailability();this.db.prepare("\n      UPDATE employees SET\n        nomComplet = ?, poste = ?, salaireCDF = ?, salaireUSD = ?,\n        dateEmbauche = ?, telephone = ?, adresse = ?, statut = ?, notes = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.dateModification,e.id)}deleteEmployee(e){this.checkAvailability();this.db.prepare("DELETE FROM employees WHERE id = ?").run(e)}}const ra=new na,aa=Object.freeze(Object.defineProperty({__proto__:null,SQLiteStorageService:na,sqliteStorageService:ra},Symbol.toStringTag,{value:"Module"}));class ia{constructor(e){this.sqlite=e,this._connectionDict=new Map}async initWebStore(){try{return await this.sqlite.initWebStore(),Promise.resolve()}catch(e){return Promise.reject(e)}}async saveToStore(e){try{return await this.sqlite.saveToStore({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async saveToLocalDisk(e){try{return await this.sqlite.saveToLocalDisk({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getFromLocalDiskToStore(e){const t=null==e||e;try{return await this.sqlite.getFromLocalDiskToStore({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async echo(e){try{const t=await this.sqlite.echo({value:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isSecretStored(){try{const e=await this.sqlite.isSecretStored();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async setEncryptionSecret(e){try{return await this.sqlite.setEncryptionSecret({passphrase:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async changeEncryptionSecret(e,t){try{return await this.sqlite.changeEncryptionSecret({passphrase:e,oldpassphrase:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async clearEncryptionSecret(){try{return await this.sqlite.clearEncryptionSecret(),Promise.resolve()}catch(e){return Promise.reject(e)}}async checkEncryptionSecret(e){try{const t=await this.sqlite.checkEncryptionSecret({passphrase:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async addUpgradeStatement(e,t){try{return e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.addUpgradeStatement({database:e,upgrade:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async createConnection(e,t,n,r,a){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.createConnection({database:e,encrypted:t,mode:n,version:r,readonly:a});const i=new sa(e,a,this.sqlite),s=a?`RO_${e}`:`RW_${e}`;return this._connectionDict.set(s,i),Promise.resolve(i)}catch(i){return Promise.reject(i)}}async closeConnection(e,t){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.closeConnection({database:e,readonly:t});const n=t?`RO_${e}`:`RW_${e}`;return this._connectionDict.delete(n),Promise.resolve()}catch(n){return Promise.reject(n)}}async isConnection(e,t){const n={};e.endsWith(".db")&&(e=e.slice(0,-3));const r=t?`RO_${e}`:`RW_${e}`;return n.result=this._connectionDict.has(r),Promise.resolve(n)}async retrieveConnection(e,t){e.endsWith(".db")&&(e=e.slice(0,-3));const n=t?`RO_${e}`:`RW_${e}`;if(this._connectionDict.has(n)){const t=this._connectionDict.get(n);return void 0!==t?Promise.resolve(t):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async getNCDatabasePath(e,t){try{const n=await this.sqlite.getNCDatabasePath({path:e,database:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async createNCConnection(e,t){try{await this.sqlite.createNCConnection({databasePath:e,version:t});const n=new sa(e,!0,this.sqlite),r=`RO_${e})`;return this._connectionDict.set(r,n),Promise.resolve(n)}catch(n){return Promise.reject(n)}}async closeNCConnection(e){try{await this.sqlite.closeNCConnection({databasePath:e});const t=`RO_${e})`;return this._connectionDict.delete(t),Promise.resolve()}catch(t){return Promise.reject(t)}}async isNCConnection(e){const t={},n=`RO_${e})`;return t.result=this._connectionDict.has(n),Promise.resolve(t)}async retrieveNCConnection(e){if(this._connectionDict.has(e)){const t=`RO_${e})`,n=this._connectionDict.get(t);return void 0!==n?Promise.resolve(n):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async isNCDatabase(e){try{const t=await this.sqlite.isNCDatabase({databasePath:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async retrieveAllConnections(){return this._connectionDict}async closeAllConnections(){const e=new Map;try{for(const t of this._connectionDict.keys()){const n=t.substring(3),r="RO_"===t.substring(0,3);await this.sqlite.closeConnection({database:n,readonly:r}),e.set(t,null)}for(const t of e.keys())this._connectionDict.delete(t);return Promise.resolve()}catch(t){return Promise.reject(t)}}async checkConnectionsConsistency(){try{const e=[...this._connectionDict.keys()],t=[],n=[];for(const a of e)t.push(a.substring(0,2)),n.push(a.substring(3));const r=await this.sqlite.checkConnectionsConsistency({dbNames:n,openModes:t});return r.result||(this._connectionDict=new Map),Promise.resolve(r)}catch(e){return this._connectionDict=new Map,Promise.reject(e)}}async importFromJson(e){try{const t=await this.sqlite.importFromJson({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isJsonValid(e){try{const t=await this.sqlite.isJsonValid({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async copyFromAssets(e){const t=null==e||e;try{return await this.sqlite.copyFromAssets({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async getFromHTTPRequest(e,t){const n=null==t||t;try{return await this.sqlite.getFromHTTPRequest({url:e,overwrite:n}),Promise.resolve()}catch(r){return Promise.reject(r)}}async isDatabaseEncrypted(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabaseEncrypted({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isInConfigEncryption(){try{const e=await this.sqlite.isInConfigEncryption();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isInConfigBiometricAuth(){try{const e=await this.sqlite.isInConfigBiometricAuth();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isDatabase(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabase({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async getDatabaseList(){try{const e=(await this.sqlite.getDatabaseList()).values;e.sort();const t={values:e};return Promise.resolve(t)}catch(e){return Promise.reject(e)}}async getMigratableDbList(e){const t=e||"default";try{const e=await this.sqlite.getMigratableDbList({folderPath:t});return Promise.resolve(e)}catch(n){return Promise.reject(n)}}async addSQLiteSuffix(e,t){const n=e||"default",r=t||[];try{const e=await this.sqlite.addSQLiteSuffix({folderPath:n,dbNameList:r});return Promise.resolve(e)}catch(a){return Promise.reject(a)}}async deleteOldDatabases(e,t){const n=e||"default",r=t||[];try{const e=await this.sqlite.deleteOldDatabases({folderPath:n,dbNameList:r});return Promise.resolve(e)}catch(a){return Promise.reject(a)}}async moveDatabasesAndAddSuffix(e,t){const n=e||"default",r=t||[];return this.sqlite.moveDatabasesAndAddSuffix({folderPath:n,dbNameList:r})}}class sa{constructor(e,t,n){this.dbName=e,this.readonly=t,this.sqlite=n}getConnectionDBName(){return this.dbName}getConnectionReadOnly(){return this.readonly}async open(){try{return await this.sqlite.open({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async close(){try{return await this.sqlite.close({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async beginTransaction(){try{const e=await this.sqlite.beginTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async commitTransaction(){try{const e=await this.sqlite.commitTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async rollbackTransaction(){try{const e=await this.sqlite.rollbackTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTransactionActive(){try{const e=await this.sqlite.isTransactionActive({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async loadExtension(e){try{return await this.sqlite.loadExtension({database:this.dbName,path:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async enableLoadExtension(e){try{return await this.sqlite.enableLoadExtension({database:this.dbName,toggle:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getUrl(){try{const e=await this.sqlite.getUrl({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getVersion(){try{const e=await this.sqlite.getVersion({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getTableList(){try{const e=await this.sqlite.getTableList({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async execute(e,t=!0,n=!0){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const r=await this.sqlite.execute({database:this.dbName,statements:e,transaction:t,readonly:!1,isSQL92:n});return Promise.resolve(r)}}catch(r){return Promise.reject(r)}}async query(e,t,n=!0){let r;try{return r=t&&t.length>0?await this.sqlite.query({database:this.dbName,statement:e,values:t,readonly:this.readonly,isSQL92:!0}):await this.sqlite.query({database:this.dbName,statement:e,values:[],readonly:this.readonly,isSQL92:n}),r=await this.reorderRows(r),Promise.resolve(r)}catch(a){return Promise.reject(a)}}async run(e,t,n=!0,r="no",a=!0){let i;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(i=t&&t.length>0?await this.sqlite.run({database:this.dbName,statement:e,values:t,transaction:n,readonly:!1,returnMode:r,isSQL92:!0}):await this.sqlite.run({database:this.dbName,statement:e,values:[],transaction:n,readonly:!1,returnMode:r,isSQL92:a}),i.changes=await this.reorderRows(i.changes),Promise.resolve(i))}catch(s){return Promise.reject(s)}}async executeSet(e,t=!0,n="no",r=!0){let a;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(a=await this.sqlite.executeSet({database:this.dbName,set:e,transaction:t,readonly:!1,returnMode:n,isSQL92:r}),a.changes=await this.reorderRows(a.changes),Promise.resolve(a))}catch(i){return Promise.reject(i)}}async isExists(){try{const e=await this.sqlite.isDBExists({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTable(e){try{const t=await this.sqlite.isTableExists({database:this.dbName,table:e,readonly:this.readonly});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isDBOpen(){try{const e=await this.sqlite.isDBOpen({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async delete(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteDatabase({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async createSyncTable(){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const e=await this.sqlite.createSyncTable({database:this.dbName,readonly:!1});return Promise.resolve(e)}}catch(e){return Promise.reject(e)}}async setSyncDate(e){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.setSyncDate({database:this.dbName,syncdate:e,readonly:!1}),Promise.resolve())}catch(t){return Promise.reject(t)}}async getSyncDate(){try{const e=await this.sqlite.getSyncDate({database:this.dbName,readonly:this.readonly});let t="";return e.syncDate>0&&(t=new Date(1e3*e.syncDate).toISOString()),Promise.resolve(t)}catch(e){return Promise.reject(e)}}async exportToJson(e,t=!1){try{const n=await this.sqlite.exportToJson({database:this.dbName,jsonexportmode:e,readonly:this.readonly,encrypted:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async deleteExportedRows(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteExportedRows({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async executeTransaction(e,t=!0){let n=0,r=!1;if(this.readonly)return Promise.reject("not allowed in read-only mode");if(await this.sqlite.beginTransaction({database:this.dbName}),r=await this.sqlite.isTransactionActive({database:this.dbName}),!r)return Promise.reject("After Begin Transaction, no transaction active");try{for(const a of e){if("object"!=typeof a||!("statement"in a))throw new Error("Error a task.statement must be provided");if("values"in a&&a.values&&a.values.length>0){const e=a.statement.toUpperCase().includes("RETURNING")?"all":"no",r=await this.sqlite.run({database:this.dbName,statement:a.statement,values:a.values,transaction:!1,readonly:!1,returnMode:e,isSQL92:t});if(r.changes.changes<0)throw new Error("Error in transaction method run ");n+=r.changes.changes}else{const e=await this.sqlite.execute({database:this.dbName,statements:a.statement,transaction:!1,readonly:!1});if(e.changes.changes<0)throw new Error("Error in transaction method execute ");n+=e.changes.changes}}n+=(await this.sqlite.commitTransaction({database:this.dbName})).changes.changes;const r={changes:{changes:n}};return Promise.resolve(r)}catch(a){const e=a.message?a.message:a;return await this.sqlite.rollbackTransaction({database:this.dbName}),Promise.reject(e)}}async reorderRows(e){const t=e;if((null==e?void 0:e.values)&&"object"==typeof e.values[0]&&Object.keys(e.values[0]).includes("ios_columns")){const n=e.values[0].ios_columns,r=[];for(let t=1;t<e.values.length;t++){const a=e.values[t],i={};for(const e of n)i[e]=a[e];r.push(i)}t.values=r}return Promise.resolve(t)}}const oa=Ur("CapacitorSQLite",{web:()=>br(()=>import("./web-B-I2arx7.js"),__vite__mapDeps([5,1,2,3,4]),import.meta.url).then(e=>new e.CapacitorSQLiteWeb),electron:()=>window.CapacitorCustomPlatform.plugins.CapacitorSQLite});class la{constructor(){n(this,"sqlite"),n(this,"db",null),n(this,"DB_NAME","smartboutique.db"),n(this,"DB_VERSION",1),n(this,"isInitialized",!1),this.sqlite=new ia(oa)}async initialize(){if(!this.isInitialized)try{if(console.log("Initializing mobile SQLite database..."),!Fr.isNativePlatform())throw new Error("SQLite is only supported on native platforms (Android/iOS)");const e=await this.sqlite.checkConnectionsConsistency(),t=(await this.sqlite.isConnection(this.DB_NAME,!1)).result;e.result&&t?this.db=await this.sqlite.retrieveConnection(this.DB_NAME,!1):this.db=await this.sqlite.createConnection(this.DB_NAME,!1,"no-encryption",this.DB_VERSION,!1),await this.db.open(),await this.createTables(),await this.createIndexes(),this.isInitialized=!0,console.log("Mobile SQLite database initialized successfully")}catch(fi){throw console.error("Failed to initialize mobile SQLite database:",fi),fi}}async createTables(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      );","CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      );","CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      );","CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      );"];for(const t of e)await this.db.execute(t)}async createIndexes(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);","CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);","CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);","CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);","CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);","CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);","CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);","CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);"];for(const t of e)await this.db.execute(t)}async ensureInitialized(){this.isInitialized||await this.initialize()}async getProducts(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM products ORDER BY nom")).values}async getProduct(e){var t;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return null==(t=(await this.db.query("SELECT * FROM products WHERE id = ?",[e])).values)?void 0:t[0]}async setProducts(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM products");for(const t of e)await this.db.run("INSERT INTO products (\n            id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n            beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n            stockMin, codeBarres, dateCreation, dateModification\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.description,t.prixAchatCDF,t.prixAchatUSD,t.prixCDF,t.prixUSD,t.beneficeUnitaireCDF,t.beneficeUnitaireUSD,t.codeQR,t.categorie,t.stock,t.stockMin,t.codeBarres,t.dateCreation,t.dateModification]);await this.db.commitTransaction()}catch(fi){throw await this.db.rollbackTransaction(),fi}}async addProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification])}async updateProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?",[e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id])}async deleteProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("DELETE FROM products WHERE id = ?",[e])}async getUsers(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM users ORDER BY nom")).values.map(e=>({...e,actif:Boolean(e.actif)}))}async setUsers(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM users");for(const t of e)await this.db.run("INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif) VALUES (?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.email,t.role,t.motDePasse,t.dateCreation,t.actif?1:0]);await this.db.commitTransaction()}catch(fi){throw await this.db.rollbackTransaction(),fi}}async getSales(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM sales ORDER BY date DESC")).values.map(e=>({...e,produits:JSON.parse(e.produits),datevente:e.date,nomClient:e.client,methodePaiement:e.typePaiement}))}async setSales(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM sales");for(const t of e)await this.db.run("INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.datevente,t.nomClient,JSON.stringify(t.produits),t.totalCDF,t.totalUSD,t.methodePaiement,t.typeVente,t.vendeur,t.numeroRecu]);await this.db.commitTransaction()}catch(fi){throw await this.db.rollbackTransaction(),fi}}async close(){this.db&&(await this.db.close(),await this.sqlite.closeConnection(this.DB_NAME,!1),this.db=null,this.isInitialized=!1)}async getStats(){var e,t,n,r,a,i;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");const[s,o,l]=await Promise.all([this.db.query("SELECT COUNT(*) as count FROM products"),this.db.query("SELECT COUNT(*) as count FROM users"),this.db.query("SELECT COUNT(*) as count FROM sales")]);return{products:(null==(t=null==(e=s.values)?void 0:e[0])?void 0:t.count)||0,users:(null==(r=null==(n=o.values)?void 0:n[0])?void 0:r.count)||0,sales:(null==(i=null==(a=l.values)?void 0:a[0])?void 0:i.count)||0,dbSize:0}}async clearAllData(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");try{await this.db.execute("DELETE FROM products"),await this.db.execute("DELETE FROM users"),await this.db.execute("DELETE FROM sales"),await this.db.execute("DELETE FROM debts"),await this.db.execute("DELETE FROM expenses"),await this.db.execute("DELETE FROM settings"),await this.db.execute("DELETE FROM sqlite_sequence"),console.log("✅ All mobile SQLite data cleared successfully")}catch(fi){throw console.error("❌ Error clearing mobile SQLite data:",fi),fi}}}const ca=new la,da=Object.freeze(Object.defineProperty({__proto__:null,MobileSQLiteStorageService:la,mobileSQLiteStorageService:ca},Symbol.toStringTag,{value:"Module"}));const ua=new class{constructor(){n(this,"MIGRATION_FLAG_KEY","smartboutique_mobile_migrated_to_sqlite")}async isMigrationCompleted(){return"true"===(await Hr.get({key:this.MIGRATION_FLAG_KEY})).value}async markMigrationCompleted(){await Hr.set({key:this.MIGRATION_FLAG_KEY,value:"true"})}async migrateToSQLite(){const e={success:!1,message:"",migratedCounts:{products:0,users:0,sales:0,debts:0,expenses:0},errors:[]};try{if(console.log("Starting mobile migration from CSV to SQLite..."),await this.isMigrationCompleted())return e.success=!0,e.message="Migration mobile déjà effectuée",e;await ca.initialize();const t=await this.extractCSVData();if(!this.validateCSVData(t))return e.errors.push("Données CSV mobiles invalides ou corrompues"),e.message="Échec de la validation des données CSV mobiles",e;await this.createBackup(t),await this.performMigration(t,e),await this.verifyMigration(t,e)?(await this.markMigrationCompleted(),e.success=!0,e.message=`Migration mobile réussie: ${e.migratedCounts.products} produits, ${e.migratedCounts.users} utilisateurs, ${e.migratedCounts.sales} ventes migrées`):e.message="Échec de la vérification de la migration mobile"}catch(fi){console.error("Mobile migration error:",fi),e.errors.push(`Erreur de migration mobile: ${fi.message}`),e.message="Échec de la migration mobile"}return e}async extractCSVData(){return console.log("Extracting data from CSV Capacitor Preferences..."),{products:await Yr.getProducts()||[],users:await Yr.getUsers()||[],sales:await Yr.getSales()||[],debts:await Yr.getDebts()||[],expenses:await Yr.getExpenses()||[]}}validateCSVData(e){try{if(!e||"object"!=typeof e)return!1;const{products:t,users:n,sales:r,debts:a,expenses:i}=e;if(t&&Array.isArray(t))for(const e of t)if(!e.id||!e.nom||"number"!=typeof e.prixCDF)return console.warn("Invalid mobile product found:",e),!1;if(n&&Array.isArray(n))for(const e of n)if(!(e.id&&e.nom&&e.email&&e.role))return console.warn("Invalid mobile user found:",e),!1;if(r&&Array.isArray(r))for(const e of r)if(!e.id||!e.datevente||!Array.isArray(e.produits))return console.warn("Invalid mobile sale found:",e),!1;return!0}catch(fi){return console.error("Mobile data validation error:",fi),!1}}async createBackup(e){try{const t={timestamp:(new Date).toISOString(),platform:"mobile",data:e};await Hr.set({key:"smartboutique_mobile_csv_backup",value:JSON.stringify(t)}),console.log("Mobile backup created successfully")}catch(fi){throw console.error("Mobile backup creation failed:",fi),new Error("Impossible de créer une sauvegarde mobile")}}async performMigration(e,t){try{const{products:n,users:r,sales:a,debts:i,expenses:s}=e;n&&n.length>0&&(await ca.setProducts(n),t.migratedCounts.products=n.length,console.log(`Migrated ${n.length} mobile products`)),r&&r.length>0&&(await ca.setUsers(r),t.migratedCounts.users=r.length,console.log(`Migrated ${r.length} mobile users`)),a&&a.length>0&&(await ca.setSales(a),t.migratedCounts.sales=a.length,console.log(`Migrated ${a.length} mobile sales`)),console.log("Mobile migration to SQLite completed")}catch(fi){throw console.error("Mobile migration execution failed:",fi),new Error(`Échec de la migration mobile: ${fi.message}`)}}async verifyMigration(e,t){try{const n=await ca.getStats();return e.products&&e.products.length!==n.products?(t.errors.push(`Nombre de produits mobile incorrect: attendu ${e.products.length}, trouvé ${n.products}`),!1):e.users&&e.users.length!==n.users?(t.errors.push(`Nombre d'utilisateurs mobile incorrect: attendu ${e.users.length}, trouvé ${n.users}`),!1):e.sales&&e.sales.length!==n.sales?(t.errors.push(`Nombre de ventes mobile incorrect: attendu ${e.sales.length}, trouvé ${n.sales}`),!1):(console.log("Mobile migration verification successful"),!0)}catch(fi){return console.error("Mobile migration verification failed:",fi),t.errors.push(`Échec de la vérification mobile: ${fi.message}`),!1}}async rollbackMigration(){try{console.log("Rolling back mobile migration...");const e=await Hr.get({key:"smartboutique_mobile_csv_backup"});if(!e.value)return console.error("No mobile backup found for rollback"),!1;const t=JSON.parse(e.value),{data:n}=t;return n.products&&await Yr.setProducts(n.products),n.users&&await Yr.setUsers(n.users),n.sales&&await Yr.setSales(n.sales),n.debts&&await Yr.setDebts(n.debts),n.expenses&&await Yr.setExpenses(n.expenses),await Hr.remove({key:this.MIGRATION_FLAG_KEY}),await ca.close(),console.log("Mobile migration rollback completed"),!0}catch(fi){return console.error("Mobile rollback failed:",fi),!1}}async getMigrationStatus(){const e=await this.isMigrationCompleted(),t=await Hr.get({key:"smartboutique_mobile_csv_backup"}),n=await Hr.get({key:"smartboutique_csv_products"});return{isCompleted:e,sqliteStats:e?await ca.getStats():void 0,csvDataExists:Boolean(n.value),backupExists:Boolean(t.value)}}async cleanupOldData(){if(!(await this.isMigrationCompleted()))return void console.warn("Cannot cleanup mobile data: migration not completed");const e=["smartboutique_csv_products","smartboutique_csv_users","smartboutique_csv_sales","smartboutique_csv_debts","smartboutique_csv_expenses","smartboutique_csv_settings"];for(const t of e)await Hr.remove({key:t});console.log("Old mobile CSV data cleaned up")}async forceMigration(){return await Hr.remove({key:this.MIGRATION_FLAG_KEY}),this.migrateToSQLite()}};const ma=new class{constructor(){n(this,"migrationChecked",!1),n(this,"mobileMigrationChecked",!1)}get storage(){return Lr()?Yr:Jr}get sqliteStorage(){return Lr()?ca:ra}async checkDesktopMigration(){if(!this.migrationChecked&&!Lr()){this.migrationChecked=!0;try{return void console.log("SQLite migration temporarily disabled - using CSV storage")}catch(fi){console.error("Desktop migration check failed:",fi)}}}async checkMobileMigration(){if(!this.mobileMigrationChecked&&Lr()){this.mobileMigrationChecked=!0;try{if(!(await ua.isMigrationCompleted())){console.log("Starting automatic mobile migration to SQLite...");const e=await ua.migrateToSQLite();e.success?console.log("Mobile migration completed successfully:",e.message):(console.error("Mobile migration failed:",e.message,e.errors),console.log("Falling back to CSV storage on mobile"))}}catch(fi){console.error("Mobile migration check failed:",fi),console.log("Falling back to CSV storage on mobile due to error")}}}async checkMigration(){Lr()?await this.checkMobileMigration():await this.checkDesktopMigration()}async set(e,t){Lr()?console.warn("Generic set method not available in CSV storage"):Jr.set(e,t)}async get(e,t){return Lr()?(console.warn("Generic get method not available in CSV storage"),t):Jr.get(e,t)}async remove(e){Lr()?console.warn("Generic remove method not available in CSV storage"):Jr.remove(e)}async clear(){Lr()?await this.clearMobileData():await this.clearDesktopData()}async clearMobileData(){try{await Yr.clearAllData();const{Preferences:e}=await br(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Gr);return{Preferences:e}},void 0,import.meta.url),{keys:t}=await e.keys(),n=t.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const r of n)await e.remove({key:r});try{await ua.isMigrationCompleted()&&await this.sqliteStorage.clearAllData()}catch(fi){console.warn("Mobile SQLite clear failed or not available:",fi)}console.log("✅ Mobile data cleared successfully")}catch(fi){throw console.error("❌ Error clearing mobile data:",fi),fi}}async clearDesktopData(){try{Jr.clear();try{ra.clearAllData()}catch(fi){console.warn("Desktop SQLite clear failed or not available:",fi)}console.log("✅ Desktop data cleared successfully")}catch(fi){throw console.error("❌ Error clearing desktop data:",fi),fi}}async getUsers(){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return await this.sqliteStorage.getUsers()}catch(fi){console.warn("Mobile SQLite failed, falling back to CSV:",fi)}return await Yr.getUsers()}return Jr.getUsers()}async setUsers(e){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return void(await this.sqliteStorage.setUsers(e))}catch(fi){console.warn("Mobile SQLite failed, falling back to CSV:",fi)}await Yr.setUsers(e)}else Jr.setUsers(e)}async getProducts(){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return await this.sqliteStorage.getProducts()}catch(fi){console.warn("Mobile SQLite failed, falling back to CSV:",fi)}return await Yr.getProducts()}return Jr.getProducts()}async setProducts(e){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return void(await this.sqliteStorage.setProducts(e))}catch(fi){console.warn("Mobile SQLite failed, falling back to CSV:",fi)}await Yr.setProducts(e)}else Jr.setProducts(e)}async getSales(){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return await this.sqliteStorage.getSales()}catch(fi){console.warn("Mobile SQLite failed, falling back to CSV:",fi)}return await Yr.getSales()}return Jr.getSales()}async setSales(e){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return void(await this.sqliteStorage.setSales(e))}catch(fi){console.warn("Mobile SQLite failed, falling back to CSV:",fi)}await Yr.setSales(e)}else Jr.setSales(e)}async getDebts(){return Lr()?await Yr.getDebts():Jr.getDebts()}async setDebts(e){Lr()?await Yr.setDebts(e):Jr.setDebts(e)}async getDettes(){return this.getDebts()}async setDettes(e){await this.setDebts(e)}async getCreances(){return this.getDebts()}async setCreances(e){await this.setDebts(e)}async getExpenses(){return Lr()?await Yr.getExpenses():Jr.getExpenses()}async setExpenses(e){Lr()?await Yr.setExpenses(e):Jr.setExpenses(e)}async getEmployeePayments(){var e,t;if(Lr())return await(null==(e=Yr.getEmployeePayments)?void 0:e.call(Yr))||[];try{return ra.getEmployeePayments()}catch(fi){return console.warn("SQLite not available for employee payments, using localStorage:",fi),(null==(t=Jr.getEmployeePayments)?void 0:t.call(Jr))||[]}}async addEmployeePayment(e){if(Lr())Yr.addEmployeePayment&&await Yr.addEmployeePayment(e);else try{ra.addEmployeePayment(e)}catch(fi){console.warn("SQLite not available for employee payments, using localStorage:",fi),Jr.addEmployeePayment&&Jr.addEmployeePayment(e)}}async updateEmployeePayment(e){if(Lr())Yr.updateEmployeePayment&&await Yr.updateEmployeePayment(e);else try{ra.updateEmployeePayment(e)}catch(fi){console.warn("SQLite not available for employee payments, using localStorage:",fi),Jr.updateEmployeePayment&&Jr.updateEmployeePayment(e)}}async deleteEmployeePayment(e){if(Lr())Yr.deleteEmployeePayment&&await Yr.deleteEmployeePayment(e);else try{ra.deleteEmployeePayment(e)}catch(fi){console.warn("SQLite not available for employee payments, using localStorage:",fi),Jr.deleteEmployeePayment&&Jr.deleteEmployeePayment(e)}}async setEmployeePayments(e){if(Lr())Yr.setEmployeePayments&&await Yr.setEmployeePayments(e);else try{ra.setEmployeePayments(e)}catch(fi){console.warn("SQLite not available for employee payments, using localStorage:",fi),Jr.setEmployeePayments&&Jr.setEmployeePayments(e)}}async getSettings(){return Lr()?await Yr.getSettings():Jr.getSettings()}async setSettings(e){Lr()?await Yr.setSettings(e):Jr.setSettings(e)}async getCurrentUser(){return Lr()?await Yr.getCurrentUser():Jr.getCurrentUser()}async setCurrentUser(e){Lr()?await Yr.setCurrentUser(e):Jr.setCurrentUser(e)}async initializeDefaultData(){console.log("🔄 Initializing fresh demo data..."),Lr()?await Yr.initializeDefaultData():Jr.initializeDefaultData();0===(await this.getDebts()).length&&await this.forceInitializeDebts();0===(await this.getSales()).length&&await this.initializeSampleSales();0===(await this.getExpenses()).length&&await this.initializeSampleExpenses(),console.log("✅ Fresh demo data initialized successfully")}async initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),r=[{id:"1",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant - Client satisfait"},{id:"2",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",datevente:n.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70}],totalCDF:196e3,totalUSD:70,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement Mobile Money - Airtel"}];await this.setSales(r),console.log("✅ Sample sales data initialized")}async initializeSampleExpenses(){const e=new Date,t=new Date(e.getTime()-6048e5),n=[{id:"1",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",dateDepense:t.toISOString(),description:"Achat de fournitures de bureau",montantCDF:28e4,montantUSD:100,categorie:"Fournitures",methodePaiement:"cash",creePar:"Super Admin",notes:"Papier, stylos, et autres fournitures",dateCreation:t.toISOString(),dateModification:t.toISOString()},{id:"2",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",dateDepense:e.toISOString(),description:"Frais de transport - Livraison",montantCDF:14e4,montantUSD:50,categorie:"Transport",methodePaiement:"mobile_money",creePar:"Gestionnaire",notes:"Transport pour livraison clients",dateCreation:e.toISOString(),dateModification:e.toISOString()}];await this.setExpenses(n),console.log("✅ Sample expenses data initialized")}async forceInitializeDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()+2592e6),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 823 456 789",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:75e3,montantTotalUSD:26.79,montantPayeCDF:25e3,montantPayeUSD:8.93,montantRestantCDF:5e4,montantRestantUSD:17.86,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:25e3,montantUSD:8.93,methodePaiement:"cash",datePaiement:e.toISOString(),notes:"Paiement partiel"}],notes:"Dette partiellement payée"}];await this.setDebts(r)}async exportData(){if(Lr()){const{csvImportExportService:e}=await br(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>hi);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();return t.data?{csvData:t.data,exportDate:(new Date).toISOString()}:{}}return Jr.exportData()}async importData(e){if(Lr()){if(e.csvData&&"string"==typeof e.csvData){const{csvImportExportService:e}=await br(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>hi);return{csvImportExportService:e}},void 0,import.meta.url);return console.log("CSV import for mobile needs full implementation"),!1}return Jr.importData(e)}return Jr.importData(e)}async migrateFromDesktop(){if(!Lr())return console.warn("Migration should only be called on mobile platform"),!1;try{if((await Yr.getUsers()).length>0)return console.log("CSV storage already has data, skipping migration"),!0;const e={users:localStorage.getItem("smartboutique_users"),products:localStorage.getItem("smartboutique_products"),sales:localStorage.getItem("smartboutique_sales"),debts:localStorage.getItem("smartboutique_debts"),expenses:localStorage.getItem("smartboutique_expenses"),settings:localStorage.getItem("smartboutique_settings"),currentUser:localStorage.getItem("smartboutique_currentUser")};let t=!1;return e.users&&(await Yr.setUsers(JSON.parse(e.users)),t=!0),e.products&&(await Yr.setProducts(JSON.parse(e.products)),t=!0),e.sales&&(await Yr.setSales(JSON.parse(e.sales)),t=!0),e.debts&&(await Yr.setDebts(JSON.parse(e.debts)),t=!0),e.expenses&&(await Yr.setExpenses(JSON.parse(e.expenses)),t=!0),e.settings&&(await Yr.setSettings(JSON.parse(e.settings)),t=!0),e.currentUser&&(await Yr.setCurrentUser(JSON.parse(e.currentUser)),t=!0),t?console.log("Successfully migrated data from desktop to mobile"):console.log("No desktop data found to migrate"),!0}catch(fi){return console.error("Error during migration:",fi),!1}}async getEmployees(){await this.checkMigration();let e=[];if(Lr()){try{await ua.isMigrationCompleted()&&(e=await this.sqliteStorage.getEmployees())}catch(fi){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fi)}0===e.length&&(e=Yr.getEmployees?await Yr.getEmployees():[])}else try{e=ra.getEmployees()}catch(fi){console.warn("SQLite not available for employees, falling back to localStorage storage:",fi),e=Jr.getEmployees?Jr.getEmployees():[]}return this.migrateEmployeeStructure(e)}migrateEmployeeStructure(e){return e.map(e=>{if(e.nom&&!e.nomComplet){const t=e.prenom?`${e.prenom} ${e.nom}`:e.nom;return{...e,nomComplet:t,nom:void 0,prenom:void 0}}return e})}async addEmployee(e){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return void(await this.sqliteStorage.addEmployee(e))}catch(fi){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fi)}if(!Yr.addEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Yr.addEmployee(e)}else try{ra.addEmployee(e)}catch(fi){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",fi),!Jr.addEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Jr.addEmployee(e)}}async updateEmployee(e){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return void(await this.sqliteStorage.updateEmployee(e))}catch(fi){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fi)}if(!Yr.updateEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Yr.updateEmployee(e)}else try{ra.updateEmployee(e)}catch(fi){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",fi),!Jr.updateEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Jr.updateEmployee(e)}}async deleteEmployee(e){if(await this.checkMigration(),Lr()){try{if(await ua.isMigrationCompleted())return void(await this.sqliteStorage.deleteEmployee(e))}catch(fi){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fi)}if(!Yr.deleteEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Yr.deleteEmployee(e)}else try{ra.deleteEmployee(e)}catch(fi){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",fi),!Jr.deleteEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Jr.deleteEmployee(e)}}};const ha=new class{constructor(){n(this,"currentUser",null),n(this,"initialized",!1)}async initialize(){this.initialized||(this.currentUser=await ma.getCurrentUser(),this.initialized=!0)}async login(e,t){await this.initialize();const n=(await ma.getUsers()).find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,await ma.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}async logout(){this.currentUser=null,await ma.remove("currentUser")}getCurrentUser(){return this.currentUser}async getCurrentUserAsync(){return await this.initialize(),this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;case"/employee-payments":return t.canViewEmployeePayments;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewEmployeePayments&&t.push({label:"Paiements Employés",path:"/employee-payments",icon:"Payment"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}async updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=await ma.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const r={...t[n],...e};return t[n]=r,await ma.setUsers(t),this.currentUser=r,await ma.setCurrentUser(r),{success:!0}}catch(fi){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}async changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}},pa=({currentUser:e,onLogout:t})=>{var n,F;const[U,T]=vt.useState(!1),k=Kn(),A=Gn(),R=e?ha.getUserPermissions():null,M=[{label:"Inventaire",path:"/products",icon:r.jsx(D,{}),permission:"canViewProducts"},{label:"Ventes",path:"/sales",icon:r.jsx(C,{}),permission:"canViewSales"},{label:"Dettes",path:"/debts",icon:r.jsx(S,{}),permission:"canViewDebts"},{label:"Plus",path:"/more",icon:r.jsx(v,{}),permission:null}],I=[{label:"Tableau de bord",path:"/dashboard",icon:r.jsx(b,{}),permission:"canViewDashboard"},{label:"Dépenses",path:"/expenses",icon:r.jsx(f,{}),permission:"canViewExpenses"},{label:"Rapports",path:"/reports",icon:r.jsx(w,{}),permission:"canViewReports"},{label:"Utilisateurs",path:"/users",icon:r.jsx(E,{}),permission:"canViewUsers"},{label:"Paramètres",path:"/settings",icon:r.jsx(P,{}),permission:"canViewSettings"}],N=e=>!e||!R||R[e];return r.jsxs(a,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[r.jsx(i,{position:"fixed",sx:{zIndex:e=>e.zIndex.drawer+1},children:r.jsxs(s,{children:[r.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),r.jsx(l,{sx:{bgcolor:"secondary.main",width:32,height:32},children:(null==(n=null==e?void 0:e.nom)?void 0:n.charAt(0))||"U"})]})}),r.jsx(a,{component:"main",sx:{flexGrow:1,pt:8,pb:7,overflow:"auto"},children:r.jsx(dr,{})}),r.jsx(c,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:3,children:r.jsx(d,{value:(()=>{const e=A.pathname;return M.find(t=>t.path===e)?e:"/more"})(),onChange:(e,t)=>{var n;"/more"===(n=t)?T(!0):k(n)},showLabels:!0,children:M.map(e=>N(e.permission)&&r.jsx(u,{label:e.label,value:e.path,icon:e.icon},e.path))})}),r.jsxs(m,{anchor:"right",open:U,onClose:()=>T(!1),PaperProps:{sx:{width:280}},children:[r.jsx(s,{}),r.jsx(a,{sx:{p:2},children:r.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:2},children:[r.jsx(l,{sx:{bgcolor:"primary.main",mr:2},children:(null==(F=null==e?void 0:e.nom)?void 0:F.charAt(0))||"U"}),r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle1",fontWeight:"bold",children:(null==e?void 0:e.nom)||"Utilisateur"}),r.jsx(o,{variant:"body2",color:"text.secondary",children:(null==e?void 0:e.role)||"Rôle"})]})]})}),r.jsx(h,{}),r.jsxs(p,{children:[I.map(e=>N(e.permission)&&r.jsxs(x,{onClick:()=>{return t=e.path,k(t),void T(!1);var t},sx:{cursor:"pointer"},children:[r.jsx(g,{children:e.icon}),r.jsx(y,{primary:e.label})]},e.path)),r.jsx(h,{sx:{my:1}}),r.jsxs(x,{onClick:()=>{t(),T(!1)},sx:{cursor:"pointer"},children:[r.jsx(g,{children:r.jsx(j,{})}),r.jsx(y,{primary:"Déconnexion"})]})]})]})]})},xa={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},ga={date:wt({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:wt({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:wt({formats:{full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},ya={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},ja=["MMM","MMMM"],Da={code:"fr",formatDistance:(e,t,n)=>{let r;const a=xa[e];return r="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",String(t)),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"dans "+r:"il y a "+r:r},formatLong:ga,formatRelative:(e,t,n,r)=>ya[e],localize:{preprocessor:(e,t)=>{if(1===e.getDate())return t;return t.some(e=>e.isToken&&ja.includes(e.value))?t.map(e=>e.isToken&&"do"===e.value?{isToken:!0,value:"d"}:e):t},ordinalNumber:(e,t)=>{const n=Number(e),r=null==t?void 0:t.unit;if(0===n)return"0";let a;return a=1===n?r&&["year","week","hour","minute","second"].includes(r)?"ère":"er":"ème",n+a},era:Et({values:{narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},defaultWidth:"wide"}),quarter:Et({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:Et({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},defaultWidth:"wide"}),day:Et({values:{narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},defaultWidth:"wide"}),dayPeriod:Et({values:{narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},defaultWidth:"wide"})},match:{ordinalNumber:Ft({matchPattern:/^(\d+)(ième|ère|ème|er|e)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e)}),era:Pt({matchPatterns:{narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:Pt({matchPatterns:{narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Pt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Pt({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Pt({matchPatterns:{narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};const Ca=new class{constructor(){n(this,"currentUser",null),this.currentUser=Jr.getCurrentUser()}login(e,t){const n=Jr.getUsers().find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,Jr.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}logout(){this.currentUser=null,Jr.remove("currentUser")}getCurrentUser(){return this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=Jr.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const r={...t[n],...e};return t[n]=r,Jr.setUsers(t),this.currentUser=r,Jr.setCurrentUser(r),{success:!0}}catch(fi){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}};const Sa=new class{constructor(){n(this,"STORAGE_KEY","notifications"),n(this,"LOW_STOCK_THRESHOLD",5),n(this,"notifications",[]),n(this,"listeners",[]),this.loadNotifications()}loadNotifications(){try{const e=localStorage.getItem(this.STORAGE_KEY);this.notifications=e?JSON.parse(e):[]}catch(fi){console.error("Erreur lors du chargement des notifications:",fi),this.notifications=[]}}saveNotifications(){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.notifications)),this.notifyListeners()}catch(fi){console.error("Erreur lors de la sauvegarde des notifications:",fi)}}notifyListeners(){this.listeners.forEach(e=>e(this.notifications))}subscribe(e){return this.listeners.push(e),e(this.notifications),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}getNotifications(){return[...this.notifications]}getUnreadCount(){return this.notifications.filter(e=>!e.lu).length}markAsRead(e){const t=this.notifications.find(t=>t.id===e);t&&!t.lu&&(t.lu=!0,this.saveNotifications())}markAllAsRead(){let e=!1;this.notifications.forEach(t=>{t.lu||(t.lu=!0,e=!0)}),e&&this.saveNotifications()}deleteNotification(e){const t=this.notifications.findIndex(t=>t.id===e);t>-1&&(this.notifications.splice(t,1),this.saveNotifications())}clearAll(){this.notifications=[],this.saveNotifications()}shouldReceiveStockNotifications(e){if(!e)return!1;const t=Ca.getUserPermissions();return"admin"===e.role||"super_admin"===e.role||!0===(null==t?void 0:t.canManageProducts)}calculateSuggestedReorder(e){return Math.max(3*e.stockMin,20)-e.stock}checkLowStock(e){const t=Ca.getCurrentUser();if(!this.shouldReceiveStockNotifications(t))return;e.filter(e=>e.stock>0&&e.stock<=this.LOW_STOCK_THRESHOLD).forEach(e=>{this.notifications.find(t=>"warning"===t.type&&t.titre.includes(e.nom)&&t.message.includes("stock bas")&&!t.lu&&Date.now()-new Date(t.dateCreation).getTime()<864e5)||this.createLowStockNotification(e)})}createLowStockNotification(e){const t=this.calculateSuggestedReorder(e),n={id:`stock-${e.id}-${Date.now()}`,type:"warning",titre:`Stock bas: ${e.nom}`,message:`Le produit "${e.nom}" a un stock critique de ${e.stock} unité(s). Stock minimum: ${e.stockMin}. Quantité suggérée à commander: ${t} unité(s).`,dateCreation:(new Date).toISOString(),lu:!1,productId:e.id,productName:e.nom,currentStock:e.stock,minimumStock:e.stockMin,suggestedReorder:t};this.notifications.unshift(n),this.saveNotifications(),this.showBrowserNotification(n)}showBrowserNotification(e){"Notification"in window&&"granted"===Notification.permission&&new Notification(`SmartBoutique - ${e.titre}`,{body:`Stock actuel: ${e.currentStock} unité(s). Commande suggérée: ${e.suggestedReorder} unité(s).`,icon:"/favicon.ico",tag:`stock-${e.productId}`})}async requestNotificationPermission(){if(!("Notification"in window))return!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;return"granted"===await Notification.requestPermission()}createNotification(e,t,n){const r={id:`notif-${Date.now()}`,type:e,titre:t,message:n,dateCreation:(new Date).toISOString(),lu:!1};this.notifications.unshift(r),this.saveNotifications()}},va=({color:e="inherit"})=>{const[t,n]=vt.useState(null),{notifications:i,unreadCount:s,markAsRead:l,markAllAsRead:d,deleteNotification:u,clearAll:m,requestPermission:j}=(()=>{const[e,t]=vt.useState([]),[n,r]=vt.useState(0);return vt.useEffect(()=>Sa.subscribe(e=>{t(e),r(Sa.getUnreadCount())}),[]),{notifications:e,unreadCount:n,markAsRead:e=>{Sa.markAsRead(e)},markAllAsRead:()=>{Sa.markAllAsRead()},deleteNotification:e=>{Sa.deleteNotification(e)},clearAll:()=>{Sa.clearAll()},requestPermission:async()=>await Sa.requestNotificationPermission()}})(),C=e=>{switch(e){case"info":default:return r.jsx(B,{color:"info"});case"warning":return r.jsx(z,{color:"warning"});case"error":return r.jsx($,{color:"error"});case"success":return r.jsx(_,{color:"success"})}},S=e=>{switch(e){case"info":return"info";case"warning":return"warning";case"error":return"error";case"success":return"success";default:return"default"}},v=Boolean(t),b=v?"notification-popover":void 0;return r.jsxs(r.Fragment,{children:[r.jsx(F,{title:"Notifications",children:r.jsx(U,{color:e,onClick:e=>{n(e.currentTarget)},children:r.jsx(T,{badgeContent:s,color:"error",children:s>0?r.jsx(k,{}):r.jsx(A,{})})})}),r.jsx(R,{id:b,open:v,anchorEl:t,onClose:()=>{n(null)},anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},children:r.jsxs(c,{sx:{width:400,maxHeight:500},children:[r.jsxs(a,{sx:{p:2,borderBottom:"1px solid",borderColor:"divider"},children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(o,{variant:"h6",children:"Notifications"}),s>0&&r.jsx(M,{label:`${s} non lue(s)`,color:"primary",size:"small"})]}),i.length>0&&r.jsxs(a,{sx:{mt:1,display:"flex",gap:1},children:[s>0&&r.jsx(I,{size:"small",startIcon:r.jsx(N,{}),onClick:d,children:"Tout marquer lu"}),r.jsx(I,{size:"small",startIcon:r.jsx(L,{}),onClick:m,color:"error",children:"Tout effacer"})]})]}),"Notification"in window&&"default"===Notification.permission&&r.jsx(O,{severity:"info",sx:{m:1},action:r.jsx(I,{size:"small",onClick:async()=>{await j()},children:"Activer"}),children:"Activez les notifications du navigateur pour les alertes de stock"}),0===i.length?r.jsxs(a,{sx:{p:3,textAlign:"center"},children:[r.jsx(A,{sx:{fontSize:48,color:"text.secondary",mb:1}}),r.jsx(o,{variant:"body2",color:"text.secondary",children:"Aucune notification"})]}):r.jsx(p,{sx:{maxHeight:350,overflow:"auto"},children:i.map((e,t)=>r.jsxs(ft.Fragment,{children:[r.jsxs(x,{button:!0,onClick:()=>(e=>{e.lu||l(e.id)})(e),sx:{bgcolor:e.lu?"transparent":"action.hover","&:hover":{bgcolor:"action.selected"}},children:[r.jsx(g,{children:e.titre.includes("Stock bas")?r.jsx(D,{color:"warning"}):C(e.type)}),r.jsx(y,{primary:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(o,{variant:"subtitle2",sx:{fontWeight:e.lu?"normal":"bold",flex:1},children:e.titre}),r.jsx(M,{label:e.type,size:"small",color:S(e.type),variant:"outlined"})]}),secondary:r.jsxs(a,{children:[r.jsx(o,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:e.message}),r.jsx(o,{variant:"caption",color:"text.secondary",children:Ut(new Date(e.dateCreation),"dd/MM/yyyy HH:mm",{locale:Da})})]})}),r.jsx(V,{children:r.jsx(F,{title:"Supprimer",children:r.jsx(U,{edge:"end",size:"small",onClick:t=>{return n=t,r=e.id,n.stopPropagation(),void u(r);var n,r},children:r.jsx(q,{fontSize:"small"})})})})]}),t<i.length-1&&r.jsx(h,{})]},e.id))})]})})]})},ba=240,fa=({currentUser:e,onLogout:t})=>{var n,c,d;if(Lr())return r.jsx(pa,{currentUser:e,onLogout:t});const[u,T]=vt.useState(!1),[k,A]=vt.useState(null),R=Kn(),M=Gn(),I=()=>{T(!u)},N=()=>{A(null)},L=ha.getNavigationItems(),O=e=>{switch(e){case"Dashboard":default:return r.jsx(b,{});case"Inventory":return r.jsx(D,{});case"PointOfSale":return r.jsx(C,{});case"AccountBalance":return r.jsx(S,{});case"Receipt":return r.jsx(f,{});case"Payment":return r.jsx(G,{});case"Assessment":return r.jsx(w,{});case"People":return r.jsx(E,{});case"Settings":return r.jsx(P,{})}},V=r.jsxs("div",{children:[r.jsx(s,{children:r.jsxs(a,{display:"flex",alignItems:"center",width:"100%",children:[r.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),r.jsx(U,{onClick:I,sx:{display:{sm:"none"}},children:r.jsx(W,{})})]})}),r.jsx(h,{}),r.jsx(p,{children:L.map(e=>r.jsx(x,{disablePadding:!0,children:r.jsxs(X,{selected:M.pathname===e.path,onClick:()=>{R(e.path),T(!1)},children:[r.jsx(g,{children:O(e.icon)}),r.jsx(y,{primary:e.label})]})},e.path))})]});return r.jsxs(a,{sx:{display:"flex"},children:[r.jsx(i,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:r.jsxs(s,{children:[r.jsx(U,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:I,sx:{mr:2,display:{sm:"none"}},children:r.jsx(v,{})}),r.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:(null==(n=L.find(e=>e.path===M.pathname))?void 0:n.label)||"SmartBoutique"}),r.jsx(va,{color:"inherit"}),r.jsx(F,{title:"Profil utilisateur",children:r.jsx(U,{color:"inherit",onClick:e=>{A(e.currentTarget)},sx:{ml:1},children:r.jsx(l,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:(null==(d=null==(c=null==e?void 0:e.nom)?void 0:c.charAt(0))?void 0:d.toUpperCase())||"U"})})}),r.jsxs(Q,{anchorEl:k,open:Boolean(k),onClose:N,onClick:N,children:[r.jsx(J,{disabled:!0,children:r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",children:null==e?void 0:e.nom}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["super_admin"===(null==e?void 0:e.role)&&"Super Administrateur","admin"===(null==e?void 0:e.role)&&"Administrateur","employee"===(null==e?void 0:e.role)&&"Employé"]})]})}),r.jsx(h,{}),r.jsxs(J,{onClick:()=>R("/settings"),children:[r.jsx(g,{children:r.jsx(H,{fontSize:"small"})}),"Mon Profil"]}),r.jsxs(J,{onClick:()=>{N(),t(),R("/login")},children:[r.jsx(g,{children:r.jsx(j,{fontSize:"small"})}),"Déconnexion"]})]})]})}),r.jsxs(a,{component:"nav",sx:{width:{sm:ba},flexShrink:{sm:0}},children:[r.jsx(m,{variant:"temporary",open:u,onClose:I,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:ba}},children:V}),r.jsx(m,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:ba}},open:!0,children:V})]}),r.jsxs(a,{component:"main",sx:{flexGrow:1,p:3,width:{sm:"calc(100% - 240px)"},minHeight:"100vh",backgroundColor:"background.default"},children:[r.jsx(s,{}),r.jsx(dr,{})]})]})},wa=({children:e,requiredPermission:t,requiredRole:n})=>ha.getCurrentUser()?n&&!ha.hasRole(n)?r.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:r.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[r.jsx(Y,{sx:{fontSize:64,color:"error.main",mb:2}}),r.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),r.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),r.jsx(O,{severity:"warning",sx:{mt:2},children:"Contactez votre administrateur pour obtenir l'accès requis."})]})}):t&&!ha.hasPermission(t)?r.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:r.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[r.jsx(Y,{sx:{fontSize:64,color:"error.main",mb:2}}),r.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),r.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),r.jsx(O,{severity:"warning",sx:{mt:2},children:"Cette fonctionnalité est réservée aux utilisateurs autorisés."})]})}):r.jsx(r.Fragment,{children:e}):r.jsx(cr,{to:"/login",replace:!0}),Ea=({onLogin:e})=>{const[t,n]=vt.useState(""),[i,s]=vt.useState(""),[c,d]=vt.useState(!1),[u,m]=vt.useState(""),[h,p]=vt.useState(!1),x=async t=>{p(!0),m("");let n={email:"",password:""};switch(t){case"admin":n={email:"<EMAIL>",password:"admin123"};break;case"manager":n={email:"<EMAIL>",password:"manager123"};break;case"employee":n={email:"<EMAIL>",password:"employee123"}}try{const t=await ha.login(n.email,n.password);t.success&&t.user?e(t.user):m(t.message||"Erreur de connexion")}catch(r){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}};return r.jsx(K,{maxWidth:"sm",sx:{height:"100vh",display:"flex",alignItems:"center"},children:r.jsxs(a,{sx:{width:"100%",px:2},children:[r.jsxs(a,{sx:{textAlign:"center",mb:4},children:[r.jsx(l,{sx:{width:80,height:80,bgcolor:"primary.main",mx:"auto",mb:2},children:r.jsx(Z,{sx:{fontSize:40}})}),r.jsx(o,{variant:"h4",component:"h1",fontWeight:"bold",color:"primary",children:"SmartBoutique"}),r.jsx(o,{variant:"subtitle1",color:"text.secondary",sx:{mt:1},children:"Gestion de boutique mobile"})]}),r.jsx(ee,{elevation:4,sx:{borderRadius:3},children:r.jsxs(te,{sx:{p:4},children:[r.jsx(o,{variant:"h5",component:"h2",textAlign:"center",sx:{mb:3},children:"Connexion"}),u&&r.jsx(O,{severity:"error",sx:{mb:3},children:u}),r.jsxs(a,{component:"form",onSubmit:async n=>{n.preventDefault(),m(""),p(!0);try{const n=await ha.login(t,i);n.success&&n.user?e(n.user):m(n.message||"Erreur de connexion")}catch(r){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}},children:[r.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:t,onChange:e=>n(e.target.value),required:!0,sx:{mb:3},InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(ae,{color:"action"})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"email",style:{fontSize:"16px"}}}),r.jsx(ne,{fullWidth:!0,label:"Mot de passe",type:c?"text":"password",value:i,onChange:e=>s(e.target.value),required:!0,sx:{mb:4},InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Y,{color:"action"})}),endAdornment:r.jsx(re,{position:"end",children:r.jsx(U,{onClick:()=>{d(!c)},edge:"end",size:"large",children:c?r.jsx(ie,{}):r.jsx(se,{})})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"current-password",style:{fontSize:"16px"}}}),r.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:h,sx:{py:2,fontSize:"1.1rem",fontWeight:"bold",borderRadius:2,mb:3},children:h?"Connexion...":"Se connecter"})]}),r.jsxs(a,{sx:{mt:3},children:[r.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mb:2},children:"Connexion rapide (Démo)"}),r.jsxs(a,{sx:{display:"flex",flexDirection:"column",gap:1},children:[r.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("admin"),disabled:h,sx:{py:1.5},children:"Super Admin"}),r.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("manager"),disabled:h,sx:{py:1.5},children:"Gestionnaire"}),r.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("employee"),disabled:h,sx:{py:1.5},children:"Employé"})]})]})]})}),r.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mt:3},children:"Version Mobile • SmartBoutique 2024"})]})})},Pa=({onLogin:e})=>{if(Lr())return r.jsx(Ea,{onLogin:e});const[t,n]=vt.useState(""),[i,s]=vt.useState(""),[c,d]=vt.useState(""),[u,m]=vt.useState(!1),p=(e,t)=>{n(e),s(t)};return r.jsx(K,{component:"main",maxWidth:"sm",children:r.jsxs(a,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",py:4},children:[r.jsxs(a,{sx:{mb:4,textAlign:"center"},children:[r.jsx(l,{sx:{mx:"auto",mb:2,bgcolor:"primary.main",width:64,height:64},children:r.jsx(Z,{sx:{fontSize:32}})}),r.jsx(o,{component:"h1",variant:"h4",gutterBottom:!0,children:"SmartBoutique"}),r.jsx(o,{variant:"subtitle1",color:"text.secondary",children:"Système de Gestion de Boutique"})]}),r.jsx(ee,{sx:{width:"100%",maxWidth:400},children:r.jsxs(te,{sx:{p:4},children:[r.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:3},children:[r.jsx(l,{sx:{mr:2,bgcolor:"secondary.main"},children:r.jsx(oe,{})}),r.jsx(o,{component:"h2",variant:"h5",children:"Connexion"})]}),c&&r.jsx(O,{severity:"error",sx:{mb:2},children:c}),r.jsxs(a,{component:"form",onSubmit:async n=>{n.preventDefault(),d(""),m(!0);try{const n=await ha.login(t,i);n.success&&n.user?e(n.user):d(n.message||"Erreur de connexion")}catch(r){d("Une erreur est survenue lors de la connexion")}finally{m(!1)}},children:[r.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",autoFocus:!0,value:t,onChange:e=>n(e.target.value),disabled:u}),r.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:"password",id:"password",autoComplete:"current-password",value:i,onChange:e=>s(e.target.value),disabled:u}),r.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:u,children:u?"Connexion...":"Se connecter"})]}),r.jsx(h,{sx:{my:3},children:r.jsx(M,{label:"Comptes de démonstration",size:"small"})}),r.jsxs(a,{sx:{display:"flex",flexDirection:"column",gap:1},children:[r.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","admin123"),disabled:u,children:"Super Admin (<EMAIL>)"}),r.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","manager123"),disabled:u,children:"Gestionnaire (<EMAIL>)"}),r.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","employee123"),disabled:u,children:"Employé (<EMAIL>)"})]})]})}),r.jsxs(a,{sx:{mt:4,textAlign:"center"},children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"© 2024 SmartBoutique. Tous droits réservés."}),r.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Version 1.0.0"})]})]})})},Fa=2800,Ua=e=>null==e||isNaN(e)?0:Math.round(100*e)/100,Ta=(e,t,n,r=2800)=>{if(null==e||isNaN(e))return 0;if((null==r||isNaN(r)||r<=0)&&(console.warn("Invalid exchange rate provided, using default:",Fa),r=Fa),t===n)return Ua(e);let a;if("CDF"===t&&"USD"===n)a=e/r;else{if("USD"!==t||"CDF"!==n)return console.error("Unsupported currency conversion:",t,"to",n),0;a=e*r}return Ua(a)},ka=(e,t=2800)=>Ta(e,"CDF","USD",t),Aa=(e,t=0,n=2)=>null==e||isNaN(e)?"0":e.toLocaleString("fr-FR",{minimumFractionDigits:t,maximumFractionDigits:n}),Ra=(e,t,n={})=>{const{showSymbol:r=!0,minimumFractionDigits:a=("USD"===t?2:0),maximumFractionDigits:i=2}=n,s=null==e||isNaN(e)?0:e,o=Aa(s,a,i);return r?"USD"===t?`$${o}`:"CDF"===t?`${o} CDF`:`${o} ${t}`:o},Ma=(e,t="valeur",n={})=>{const{allowZero:r=!1,allowNegative:a=!1,minValue:i=null,maxValue:s=null}=n,o=[];return null==e||isNaN(e)?(o.push(`${t} doit être un nombre valide`),{isValid:!1,errors:o}):(!a&&e<0&&o.push(`${t} ne peut pas être négatif`),r||0!==e||o.push(`${t} doit être supérieur à zéro`),null!==i&&e<i&&o.push(`${t} doit être supérieur ou égal à ${i}`),null!==s&&e>s&&o.push(`${t} doit être inférieur ou égal à ${s}`),{isValid:0===o.length,errors:o})},Ia=(e,t=0,n="Erreur de calcul financier")=>{try{const n=e();return null==n||isNaN(n)?t:n}catch(fi){return console.error(n,fi),t}},Na="Le prix ne peut pas être négatif",La=(e,t,n=2800)=>{const r=Ua((a=t,i=e,Ia(()=>a-i,0,"Erreur lors du calcul du bénéfice unitaire")));var a,i;return{beneficeUnitaireCDF:r,beneficeUnitaireUSD:Ua(ka(r,n))}},Oa=(e,t)=>{const n=((e,t,n={})=>{const{purchaseFieldName:r="Le prix d'achat",sellingFieldName:a="Le prix de vente"}=n,i=Ma(e,r),s=Ma(t,a),o=[...i.errors,...s.errors];return i.isValid&&s.isValid&&t<=e&&o.push("Le prix de vente doit être supérieur au prix d'achat"),{isValid:0===o.length,errors:o}})(e,t);return{isValid:n.isValid,errorMessage:n.errors.length>0?n.errors[0]:void 0}},Va=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,qa=(e,t)=>{console.log("🔍 Revenue Analytics Debug:",{totalProducts:e.length,totalSales:t.length,productsWithPricing:e.filter(e=>e.prixCDF&&e.prixAchatCDF).length,productsWithStock:e.filter(e=>e.stock>0).length,profitableProducts:e.filter(e=>e.prixCDF&&e.prixAchatCDF&&e.prixCDF-e.prixAchatCDF>0).length});const n=(e=>Ia(()=>{let t=0,n=0;return e.forEach(e=>{if(e.prixCDF&&e.prixAchatCDF&&e.stock>0){const n=e.prixCDF-e.prixAchatCDF;n>0&&(t+=Ua(n*e.stock))}if(e.prixUSD&&e.prixAchatUSD&&e.stock>0){const t=e.prixUSD-e.prixAchatUSD;t>0&&(n+=Ua(t*e.stock))}else if(e.prixCDF&&e.prixAchatCDF&&e.stock>0){const t=e.prixCDF-e.prixAchatCDF;if(t>0){const r=ka(t,Fa);n+=Ua(r*e.stock)}}}),{totalRevenueCDF:Ua(t),totalRevenueUSD:Ua(n)}},{totalRevenueCDF:0,totalRevenueUSD:0},"Erreur lors du calcul des revenus d'inventaire"))(e),r=((e,t)=>Ia(()=>{let n=0,r=0;return e.forEach(e=>{e.produits.forEach(e=>{const a=t.find(t=>t.id===e.produitId);if(a&&e.prixUnitaireCDF&&a.prixAchatCDF){const t=e.prixUnitaireCDF-a.prixAchatCDF;t>0&&(n+=Ua(t*e.quantite))}if(a&&e.prixUnitaireUSD&&a.prixAchatUSD){const t=e.prixUnitaireUSD-a.prixAchatUSD;t>0&&(r+=Ua(t*e.quantite))}else if(a&&e.prixUnitaireCDF&&a.prixAchatCDF){const t=e.prixUnitaireCDF-a.prixAchatCDF;if(t>0){const n=ka(t,Fa);r+=Ua(n*e.quantite)}}})}),{realizedRevenueCDF:Ua(n),realizedRevenueUSD:Ua(r)}},{realizedRevenueCDF:0,realizedRevenueUSD:0},"Erreur lors du calcul des revenus réalisés"))(t,e),a=(e=>{const t={};return e.forEach(e=>{if(t[e.categorie]||(t[e.categorie]={revenueCDF:0,revenueUSD:0,productCount:0}),e.prixCDF&&e.prixAchatCDF&&e.stock>0){const n=e.prixCDF-e.prixAchatCDF;n>0&&(t[e.categorie].revenueCDF+=Ua(n*e.stock),t[e.categorie].productCount+=1)}if(e.prixUSD&&e.prixAchatUSD&&e.stock>0){const n=e.prixUSD-e.prixAchatUSD;n>0&&(t[e.categorie].revenueUSD+=Ua(n*e.stock))}else if(e.prixCDF&&e.prixAchatCDF&&e.stock>0){const n=e.prixCDF-e.prixAchatCDF;if(n>0){const r=ka(n,Fa);t[e.categorie].revenueUSD+=Ua(r*e.stock)}}}),t})(e),i=((e,t=10)=>e.filter(e=>e.prixCDF&&e.prixAchatCDF&&e.stock>0).filter(e=>e.prixCDF-e.prixAchatCDF>0).sort((e,t)=>{const n=(e.prixCDF-e.prixAchatCDF)*e.stock;return(t.prixCDF-t.prixAchatCDF)*t.stock-n}).slice(0,t))(e,5),s=e.filter(e=>e.prixCDF&&e.prixAchatCDF&&e.prixCDF-e.prixAchatCDF>0).length;return console.log("💰 Calculated Revenue:",{potentialProfitCDF:n.totalRevenueCDF,realizedProfitCDF:r.realizedRevenueCDF,topProductsCount:i.length,totalProductsWithProfit:s}),{totalInventoryRevenueCDF:n.totalRevenueCDF,totalInventoryRevenueUSD:n.totalRevenueUSD,realizedRevenueCDF:r.realizedRevenueCDF,realizedRevenueUSD:r.realizedRevenueUSD,categoryBreakdown:a,topProducts:i,totalProductsWithProfit:s}},Ba=(e,t)=>Ra(e,t),_a=(e,t=2800)=>{const n=null==e||isNaN(e)?0:e,r=null==t||isNaN(t)||t<=0?Fa:t,a=Ua(ka(n,r));return{primaryAmount:Ra(n,"CDF",{showSymbol:!1}),secondaryAmount:Ra(a,"USD",{showSymbol:!1}),primaryCurrency:"CDF",secondaryCurrency:"USD"}},$a=e=>0===e.stock?"out_of_stock":e.stock<=e.stockMin?"low_stock":"in_stock",za=()=>"123"+Date.now().toString().slice(-10),Wa=()=>{const e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`SB${e.slice(-8)}${t}`},Xa=(e,t)=>ka(e,t),Qa=(e,t)=>((e,t=2800)=>Ta(e,"USD","CDF",t))(e,t),Ja=(e,t)=>ka(e,t),Ha=({analytics:e,products:t,todaySalesData:n,debtsData:i,isLoading:s=!1})=>{if(s)return r.jsxs(a,{children:[r.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),r.jsx(le,{})]});if(0===e.totalProductsWithProfit)return r.jsxs(a,{children:[r.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),r.jsx(O,{severity:"info",children:"Aucun produit avec prix d'achat et de vente configurés. Ajoutez des prix d'achat aux produits pour voir l'analyse des profits."})]});const l=Object.entries(e.categoryBreakdown);return r.jsxs(a,{children:[r.jsx(o,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:"Profit - Analyse des Bénéfices"}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Potentiel"}),r.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_a(e.totalInventoryRevenueCDF,2800).primaryAmount," ",_a(e.totalInventoryRevenueCDF,2800).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",_a(e.totalInventoryRevenueCDF,2800).secondaryAmount]})]}),r.jsx(D,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Réalisé"}),r.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_a(e.realizedRevenueCDF,2800).primaryAmount," ",_a(e.realizedRevenueCDF,2800).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"success.main",children:["≈ $",_a(e.realizedRevenueCDF,2800).secondaryAmount]})]}),r.jsx(de,{color:"success",sx:{fontSize:40}})]})})})}),n&&r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),r.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_a(n.revenusCDF,2800).primaryAmount," ",_a(n.revenusCDF,2800).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",_a(n.revenusCDF,2800).secondaryAmount]}),r.jsxs(o,{variant:"caption",color:"textSecondary",children:[n.nombreVentes," vente",1!==n.nombreVentes?"s":""]})]}),r.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),i&&r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total des Dettes"}),r.jsxs(o,{variant:"h6",color:"error",fontWeight:"medium",children:[_a(i.montantDettesTotalCDF,2800).primaryAmount," ",_a(i.montantDettesTotalCDF,2800).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"error",children:["≈ $",_a(i.montantDettesTotalCDF,2800).secondaryAmount]}),r.jsxs(o,{variant:"caption",color:"textSecondary",children:[i.dettesActives," dette",1!==i.dettesActives?"s":""," active",1!==i.dettesActives?"s":""]})]}),r.jsx(S,{color:"warning",sx:{fontSize:40}})]})})})})]}),r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Produits les Plus Rentables"}),r.jsx(te,{children:r.jsx(me,{children:r.jsxs(he,{size:"small",children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Produit"}),r.jsx(ge,{align:"right",children:"Profit Potentiel"})]})}),r.jsx(ye,{children:e.topProducts.map(e=>{const t=((e.prixCDF||0)-(e.prixAchatCDF||0))*e.stock;return r.jsxs(xe,{children:[r.jsx(ge,{children:r.jsxs(a,{children:[r.jsx(o,{variant:"body2",fontWeight:"medium",children:e.nom}),r.jsxs(o,{variant:"caption",color:"textSecondary",children:["Stock: ",e.stock]})]})}),r.jsx(ge,{align:"right",children:r.jsx(o,{variant:"body2",children:Va(t,"CDF")})})]},e.id)})})]})})})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Profit par Catégorie"}),r.jsx(te,{children:r.jsx(me,{children:r.jsxs(he,{size:"small",children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Catégorie"}),r.jsx(ge,{align:"right",children:"Produits"}),r.jsx(ge,{align:"right",children:"Profit (CDF)"})]})}),r.jsx(ye,{children:l.sort(([,e],[,t])=>t.revenueCDF-e.revenueCDF).map(([e,t])=>r.jsxs(xe,{children:[r.jsx(ge,{children:r.jsx(M,{label:e,size:"small"})}),r.jsx(ge,{align:"right",children:t.productCount}),r.jsxs(ge,{align:"right",children:[r.jsx(o,{variant:"body2",children:Va(t.revenueCDF,"CDF")}),r.jsx(o,{variant:"caption",color:"textSecondary",children:Va(t.revenueUSD,"USD")})]})]},e))})]})})})]})})]})]})},Ga=()=>{const[e,t]=vt.useState(null),[n,i]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(null),[m,h]=vt.useState(null),[j,S]=vt.useState(null),[v,b]=vt.useState({tauxChangeUSDCDF:2800}),[f,w]=vt.useState("jour"),E=ha.getUserPermissions(),P=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(fi){return console.warn("Error parsing date:",e,fi),null}};vt.useEffect(()=>{(async()=>{try{const e=await ma.getSettings();b(e),await U()}catch(fi){console.error("Error loading data:",fi),await U()}})()},[]);const F=(e,t)=>{switch(e){case"jour":default:return t.ventesDuJour;case"semaine":return t.ventesDeLaSemaine;case"mois":return t.ventesDuMois}},U=async()=>{const e=await ma.getProducts(),n=await ma.getSales(),r=await ma.getDebts(),a=await ma.getSettings(),s=new Date,o=Tt(s),c=kt(s),d=At(s,7),u=At(s,30),m=n.filter(e=>{const t=P(e.datevente);return!!t&&(Rt(t,o)&&Mt(t,c))}),h=n.filter(e=>{const t=P(e.datevente);return!!t&&Rt(t,d)}),p=n.filter(e=>{const t=P(e.datevente);return!!t&&Rt(t,u)}),x=e.filter(e=>e.stock>0),g=e.filter(e=>e.stock<=e.stockMin&&e.stock>0),y=e.filter(e=>0===e.stock),j=r.filter(e=>"active"===e.statut),D=r.filter(e=>"overdue"===e.statut),C=Ua(m.reduce((e,t)=>e+t.totalCDF,0)),v=Ua(h.reduce((e,t)=>e+t.totalCDF,0)),b=Ua(p.reduce((e,t)=>e+t.totalCDF,0)),f=Ua(e.reduce((e,t)=>e+t.prixCDF*t.stock,0)),w=Ua(j.reduce((e,t)=>e+t.montantRestantCDF,0)),F=a.tauxChangeUSDCDF||Fa,U={ventesDuJour:{nombreVentes:m.length,revenusCDF:C,revenusUSD:ka(C,F)},ventesDeLaSemaine:{nombreVentes:h.length,revenusCDF:v,revenusUSD:ka(v,F)},ventesDuMois:{nombreVentes:p.length,revenusCDF:b,revenusUSD:ka(b,F)},articlesActifs:x.length,produitsStockBas:g.length,articlesEnRupture:y.length,valeurInventaireCDF:f,valeurInventaireUSD:f/a.tauxChangeUSDCDF,dettesActives:j.length,dettesEnRetard:D.length,montantDettesTotalCDF:w,montantDettesTotalUSD:w/a.tauxChangeUSDCDF};if(t(U),i(g.slice(0,5)),l(n.slice(-5).reverse()),E.canViewRevenue){const t=qa(e,n);S(t)}T(n),k(n)},T=e=>{const t=Array.from({length:14},(e,t)=>{const n=At(new Date,13-t);return{date:n,label:Ut(n,"dd/MM",{locale:Da}),sales:0,revenue:0}});e.forEach(e=>{const n=P(e.datevente);if(!n)return;const r=t.find(e=>Ut(e.date,"yyyy-MM-dd")===Ut(n,"yyyy-MM-dd"));r&&(r.sales+=1,r.revenue+=e.totalCDF)}),u({labels:t.map(e=>e.label),datasets:[{label:"Profit (CDF)",data:t.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]})},k=e=>{const t=e.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+1,e),{});h({labels:["Cash","Banque","Mobile Money"],datasets:[{data:[t.cash||0,t.banque||0,t.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]})},A=e=>0===e.stock?r.jsx($,{color:"error"}):e.stock<=e.stockMin?r.jsx(z,{color:"warning"}):r.jsx(_,{color:"success"});return e?r.jsxs(a,{children:[r.jsx(o,{variant:"h4",gutterBottom:!0,children:"Tableau de bord"}),E.canViewRevenue&&j&&r.jsx(a,{sx:{mb:4},children:r.jsx(Ha,{analytics:j,products:n,todaySalesData:null==e?void 0:e.ventesDuJour,debtsData:e?{montantDettesTotalCDF:e.montantDettesTotalCDF,montantDettesTotalUSD:e.montantDettesTotalUSD,dettesActives:e.dettesActives}:void 0,isLoading:!j})}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1,children:[r.jsxs(je,{size:"small",sx:{minWidth:120},children:[r.jsx(De,{children:"Période"}),r.jsxs(Ce,{value:f,label:"Période",onChange:e=>w(e.target.value),children:[r.jsx(J,{value:"jour",children:"Jour"}),r.jsx(J,{value:"semaine",children:"Semaine"}),r.jsx(J,{value:"mois",children:"Mois"})]})]}),r.jsx(C,{color:"primary",sx:{fontSize:40}})]}),r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:(e=>{switch(e){case"jour":default:return"Ventes du jour";case"semaine":return"Ventes de la semaine";case"mois":return"Ventes du mois"}})(f)}),r.jsx(o,{variant:"h6",children:F(f,e).nombreVentes}),E.canViewFinancials&&r.jsxs(r.Fragment,{children:[r.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_a(F(f,e).revenusCDF,v.tauxChangeUSDCDF).primaryAmount," ",_a(F(f,e).revenusCDF,v.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(F(f,e).revenusCDF,v.tauxChangeUSDCDF).secondaryAmount]})]})]})]})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes de la semaine"}),r.jsx(o,{variant:"h6",children:e.ventesDeLaSemaine.nombreVentes}),E.canViewFinancials&&r.jsxs(r.Fragment,{children:[r.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_a(e.ventesDeLaSemaine.revenusCDF,v.tauxChangeUSDCDF).primaryAmount," ",_a(e.ventesDeLaSemaine.revenusCDF,v.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(e.ventesDeLaSemaine.revenusCDF,v.tauxChangeUSDCDF).secondaryAmount]})]})]}),r.jsx(Se,{color:"success",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Articles actifs"}),r.jsx(o,{variant:"h6",children:e.articlesActifs}),e.produitsStockBas>0&&r.jsx(M,{label:`${e.produitsStockBas} stock bas`,color:"warning",size:"small"})]}),r.jsx(D,{color:"info",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),E.canViewFinancials?r.jsxs(r.Fragment,{children:[r.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[_a(e.valeurInventaireCDF,v.tauxChangeUSDCDF).primaryAmount," ",_a(e.valeurInventaireCDF,v.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_a(e.valeurInventaireCDF,v.tauxChangeUSDCDF).secondaryAmount]})]}):r.jsxs(o,{variant:"h6",children:[e.articlesActifs," articles"]})]}),r.jsx(D,{color:"success",sx:{fontSize:40}})]})})})})]}),e.articlesEnRupture>0&&r.jsx(ce,{container:!0,spacing:3,sx:{mb:3},children:r.jsx(ce,{item:!0,xs:12,children:r.jsx(O,{severity:"error",sx:{mb:2},children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{variant:"h6",component:"div",children:"Articles en rupture"}),r.jsxs(o,{variant:"body2",children:[e.articlesEnRupture," article",e.articlesEnRupture>1?"s":""," en rupture de stock nécessite",e.articlesEnRupture>1?"nt":""," un réapprovisionnement urgent"]})]}),r.jsx($,{sx:{fontSize:40}})]})})})}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,md:8,children:r.jsxs(c,{sx:{p:2},children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Tendance des ventes (14 derniers jours)"}),d&&r.jsx($t,{data:d,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})]})}),r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsxs(c,{sx:{p:2},children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Méthodes de paiement"}),m&&r.jsx(zt,{data:m,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})]})})]}),r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(c,{sx:{p:2},children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Produits en stock bas"}),0===n.length?r.jsx(O,{severity:"success",children:"Aucun produit en stock bas"}):r.jsx(p,{dense:!0,children:n.map(e=>r.jsxs(x,{children:[r.jsx(g,{children:A(e)}),r.jsx(y,{primary:e.nom,secondary:`Stock: ${e.stock} (Min: ${e.stockMin})`})]},e.id))})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(c,{sx:{p:2},children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Ventes récentes"}),0===s.length?r.jsx(O,{severity:"info",children:"Aucune vente récente"}):r.jsx(p,{dense:!0,children:s.map(e=>r.jsx(x,{children:r.jsx(y,{primary:E.canViewFinancials?`${e.nomClient} - ${_a(e.totalCDF,v.tauxChangeUSDCDF).primaryAmount} ${_a(e.totalCDF,v.tauxChangeUSDCDF).primaryCurrency}`:e.nomClient,secondary:(()=>{const t=P(e.datevente),n=t?Ut(t,"dd/MM/yyyy HH:mm",{locale:Da}):"Date invalide";if(E.canViewFinancials){return`${n} • ≈ $${_a(e.totalCDF,v.tauxChangeUSDCDF).secondaryAmount}`}return n})()})},e.id))})]})})]})]}):r.jsx(o,{children:"Chargement..."})},Ya=({label:e,value:t,onChange:n,min:i=0,max:s=1e6,step:l=100,exchangeRate:c,disabled:d=!1,required:u=!1,error:m=!1,helperText:h,showSlider:p=!0,allowUSDInput:x=!0,onCurrencyModeChange:g})=>{const[y,j]=vt.useState("CDF"),[D,C]=vt.useState(""),[S,v]=vt.useState(t);vt.useEffect(()=>{if(v(t),"CDF"===y)C(t.toString());else{const e=Xa(t,c);C(e.toFixed(2))}},[t,y,c]);const b=Xa(S,c),f=S;return r.jsxs(je,{fullWidth:!0,disabled:d,children:[r.jsxs(ve,{component:"legend",sx:{mb:1},children:[e," ",u&&"*"]}),r.jsxs(ce,{container:!0,spacing:2,children:[x&&r.jsx(ce,{item:!0,xs:12,children:r.jsxs(be,{value:y,exclusive:!0,onChange:(e,t)=>{if(t&&t!==y){if(j(t),"USD"===t){const e=Xa(S,c);C(e.toFixed(2))}else C(S.toString());g&&g(t)}},size:"small",disabled:d,children:[r.jsx(fe,{value:"CDF",children:"Saisie en CDF"}),r.jsx(fe,{value:"USD",children:"Saisie en USD"})]})}),r.jsx(ce,{item:!0,xs:12,md:p?6:12,children:r.jsx(ne,{fullWidth:!0,label:`Montant (${y})`,type:"number",value:D,onChange:e=>{const t=e.target.value;C(t);const r=parseFloat(t)||0;if(Ma(r,"Le montant",{allowZero:!0,allowNegative:!1,minValue:i,maxValue:s}).isValid){let e;e="CDF"===y?r:Qa(r,c),e=Math.max(i,Math.min(s,e)),v(e),n(e)}},disabled:d,error:m,helperText:h,inputProps:{min:"CDF"===y?i:Xa(i,c),max:"CDF"===y?s:Xa(s,c),step:"CDF"===y?l:.01},InputProps:{startAdornment:r.jsx(re,{position:"start",children:"USD"===y?"$":""}),endAdornment:r.jsx(re,{position:"end",children:"CDF"===y?"CDF":"USD"})}})}),p&&r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(a,{sx:{px:2,pt:3},children:[r.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Curseur (CDF)"}),r.jsx(we,{value:S,onChange:(e,t)=>{const r=Array.isArray(t)?t[0]:t;if(v(r),"CDF"===y)C(r.toString());else{const e=Xa(r,c);C(e.toFixed(2))}n(r)},min:i,max:s,step:l,disabled:d,valueLabelDisplay:"auto",valueLabelFormat:e=>Ba(e,"CDF")})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(a,{sx:{p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:[r.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Équivalences:"}),r.jsx(o,{variant:"body1",color:"primary",fontWeight:"medium",children:Ba(f,"CDF")}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",Ba(b,"USD")]})]})})]})]})},Ka=({value:e,onChange:t,min:n=1,max:i=999999,disabled:s=!1,size:o="small",showButtons:l=!0,allowDirectInput:c=!0,label:d,error:u=!1,helperText:m})=>{const[h,p]=vt.useState(e.toString());vt.useEffect(()=>{p(e.toString())},[e]);const x=r=>{const a=r.target.value;if(p(a),""===a)return;const s=parseFloat(a);if(!isNaN(s)){const r=Math.round(s),a=Math.max(n,Math.min(i,r));r>=n&&r<=i?t(r):a!==e&&t(a)}},g=()=>{if(""===h||isNaN(parseFloat(h))){const r=e||n;return p(r.toString()),void(r!==e&&t(r))}const r=Math.round(parseFloat(h)),a=Math.max(n,Math.min(i,r));p(a.toString()),a!==e&&t(a)},y=()=>{const n=Math.min(i,e+1);t(n)},j=()=>{const r=Math.max(n,e-1);t(r)},D=e=>{e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase())||(/[0-9.]/.test(e.key)||["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab","Enter","Home","End","Escape","."].includes(e.key)||e.preventDefault(),"."===e.key&&h.includes(".")&&e.preventDefault())};return!c&&l?r.jsxs(a,{display:"flex",alignItems:"center",gap:.5,children:[r.jsx(U,{size:o,onClick:j,disabled:s||e<=n,children:r.jsx(Ee,{fontSize:o})}),r.jsx(a,{sx:{minWidth:"small"===o?"30px":"40px",textAlign:"center",fontWeight:"medium",fontSize:"small"===o?"0.875rem":"1rem"},children:e}),r.jsx(U,{size:o,onClick:y,disabled:s||e>=i,children:r.jsx(Pe,{fontSize:o})})]}):c&&!l?r.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:D,disabled:s,error:u,helperText:m,inputProps:{min:n,max:i,step:1},sx:{minWidth:"80px"}}):r.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:D,disabled:s,error:u,helperText:m||(l?"Tapez directement la quantité (recommandé) ou utilisez +/-":"Tapez directement la quantité désirée"),placeholder:"Tapez la quantité...",inputProps:{min:n,max:i,step:1,style:{textAlign:"center",fontSize:"small"===o?"0.875rem":"1rem",fontWeight:500}},InputProps:{startAdornment:l?r.jsx(re,{position:"start",children:r.jsx(U,{size:o,onClick:j,disabled:s||e<=n,edge:"start",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:r.jsx(Ee,{fontSize:o})})}):void 0,endAdornment:l?r.jsx(re,{position:"end",children:r.jsx(U,{size:o,onClick:y,disabled:s||e>=i,edge:"end",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:r.jsx(Pe,{fontSize:o})})}):void 0},sx:{minWidth:l?"160px":"100px","& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"primary.main"},"&.Mui-focused fieldset":{borderWidth:2,borderColor:"primary.main"},"& input":{cursor:"text","&:focus":{backgroundColor:"rgba(25, 118, 210, 0.04)"}}}}})},Za=()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(""),[m,h]=vt.useState(""),[p,x]=vt.useState("all"),[g,y]=vt.useState(0),[j,C]=vt.useState(10),[S,v]=vt.useState(!1),[b,f]=vt.useState(null),[w,E]=vt.useState({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0}),[P,T]=vt.useState(""),[k,A]=vt.useState(""),[R,N]=vt.useState(!1),[L,V]=vt.useState(""),[B,W]=vt.useState({tauxChangeUSDCDF:2800}),X=ha.getUserPermissions();vt.useEffect(()=>{Q()},[]);vt.useEffect(()=>{e.length>0&&Sa.checkLowStock(e)},[e]),vt.useEffect(()=>{H()},[e,d,m,p]),vt.useEffect(()=>{-1===j&&C(s.length||1)},[s.length,j]);const Q=async()=>{try{const e=await ma.getProducts(),n=await ma.getSettings(),r=e.map(e=>{const t=(new Date).toISOString();return{...e,dateCreation:e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())?e.dateCreation:t,dateModification:e.dateModification&&!isNaN(new Date(e.dateModification).getTime())?e.dateModification:t}});t(r),i(n.categories),W(n)}catch(e){console.error("Error loading products:",e),T("Erreur lors du chargement des produits")}},H=()=>{let t=e;d&&(t=t.filter(e=>e.nom.toLowerCase().includes(d.toLowerCase())||e.codeQR.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase()))),m&&(t=t.filter(e=>e.categorie===m)),"all"!==p&&(t=t.filter(e=>$a(e)===p)),l(t)},G=e=>{switch(e){case"out_of_stock":return"error";case"low_stock":return"warning";case"in_stock":return"success";default:return"default"}},Y=e=>{switch(e){case"out_of_stock":return r.jsx($,{});case"low_stock":return r.jsx(z,{});case"in_stock":return r.jsx(_,{});default:return r.jsx(D,{})}},K=e=>{switch(e){case"out_of_stock":return"Rupture";case"low_stock":return"Stock bas";case"in_stock":return"En stock";default:return"Inconnu"}},Z=e=>{e?(f(e),E({nom:e.nom,description:e.description,prixAchatCDF:e.prixAchatCDF||0,prixCDF:e.prixCDF,categorie:e.categorie,stock:e.stock,stockMin:e.stockMin,quantiteEnStock:e.quantiteEnStock||e.stock,coutAchatStockCDF:e.coutAchatStockCDF||e.prixAchatCDF*e.stock,prixParPieceCDF:e.prixParPieceCDF||e.prixCDF})):(f(null),E({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0})),v(!0),T(""),A(""),setTimeout(()=>{document.querySelectorAll('div[role="dialog"] input, div[role="dialog"] textarea').forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})},100)},ae=()=>{v(!1),f(null),T(""),A("")},ie=e.length,se=e.filter(e=>"in_stock"===$a(e)).length,oe=e.filter(e=>"low_stock"===$a(e)).length,le=e.reduce((e,t)=>e+t.prixCDF*t.stock,0);return r.jsxs(a,{children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[r.jsx(o,{variant:"h4",children:"Inventaire"}),X.canManageProducts&&r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>Z(),children:"Nouveau Produit"})]}),k&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>A(""),children:k}),P&&r.jsx(O,{severity:"error",sx:{mb:2},action:r.jsx(I,{color:"inherit",size:"small",onClick:()=>{window.confirm("Cela va supprimer toutes les données et réinitialiser l'application. Continuer?")&&(localStorage.clear(),window.location.reload())},children:"Réinitialiser les données"}),children:P}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Produits"}),r.jsx(o,{variant:"h6",children:ie})]}),r.jsx(D,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Stock"}),r.jsx(o,{variant:"h6",color:"success.main",children:se})]}),r.jsx(_,{color:"success",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Stock Bas"}),r.jsx(o,{variant:"h6",color:"warning.main",children:oe})]}),r.jsx(z,{color:"warning",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),r.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[_a(le,B.tauxChangeUSDCDF).primaryAmount," ",_a(le,B.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_a(le,B.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(D,{color:"info",sx:{fontSize:40}})]})})})})]}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom, Code QR ou description...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Fe,{})})}})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Catégorie"}),r.jsxs(Ce,{value:m,label:"Catégorie",onChange:e=>h(e.target.value),children:[r.jsx(J,{value:"",children:"Toutes les catégories"}),n.map(e=>r.jsx(J,{value:e.nom,children:e.nom},e.id))]})]})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Statut Stock"}),r.jsxs(Ce,{value:p,label:"Statut Stock",onChange:e=>x(e.target.value),children:[r.jsx(J,{value:"all",children:"Tous"}),r.jsx(J,{value:"in_stock",children:"En stock"}),r.jsx(J,{value:"low_stock",children:"Stock bas"}),r.jsx(J,{value:"out_of_stock",children:"Rupture"})]})]})}),r.jsx(ce,{item:!0,xs:12,md:2,children:r.jsxs(a,{display:"flex",gap:1,children:[r.jsx(F,{title:"Exporter CSV",children:r.jsx(U,{onClick:()=>{const e=`SmartBoutique_Produits_${(new Date).toISOString().split("T")[0]}.csv`;Or.downloadCSV(s,Vr,e),A("Produits exportés en CSV avec succès (compatible Excel)"),setTimeout(()=>A(""),3e3)},children:r.jsx(Ue,{})})}),r.jsx(F,{title:"Importer CSV",children:r.jsx(U,{onClick:()=>N(!0),children:r.jsx(Te,{})})})]})})]})}),r.jsxs(me,{component:c,children:[r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Produit"}),r.jsx(ge,{children:"Code QR"}),r.jsx(ge,{children:"Catégorie"}),r.jsx(ge,{align:"right",children:"Prix de Vente"}),r.jsx(ge,{align:"center",children:"Stock"}),r.jsx(ge,{align:"center",children:"Qté Stock"}),r.jsx(ge,{align:"right",children:"Coût Stock"}),r.jsx(ge,{align:"center",children:"Statut"}),r.jsx(ge,{children:"Dernière Modif."}),X.canManageProducts&&r.jsx(ge,{align:"center",children:"Actions"})]})}),r.jsx(ye,{children:(-1===j?s:s.slice(g*j,g*j+j)).map(n=>{const i=$a(n);return r.jsxs(xe,{hover:!0,onClick:()=>Z(n),sx:{cursor:"pointer"},children:[r.jsx(ge,{children:r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",children:n.nom}),r.jsx(o,{variant:"caption",color:"text.secondary",children:n.description})]})}),r.jsx(ge,{children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[n.codeQR,r.jsx(F,{title:"Code QR",children:r.jsx(U,{size:"small",children:r.jsx(ke,{fontSize:"small"})})})]})}),r.jsx(ge,{children:n.categorie}),r.jsx(ge,{align:"right",children:r.jsxs(a,{children:[r.jsxs(o,{variant:"body2",fontWeight:"medium",children:[_a(n.prixCDF,B.tauxChangeUSDCDF).primaryAmount," ",_a(n.prixCDF,B.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(n.prixCDF,B.tauxChangeUSDCDF).secondaryAmount]})]})}),r.jsx(ge,{align:"center",children:r.jsxs(a,{children:[r.jsx(o,{variant:"body2",children:n.stock}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["Min: ",n.stockMin]})]})}),r.jsx(ge,{align:"center",children:r.jsxs(a,{children:[r.jsx(o,{variant:"body2",fontWeight:"medium",children:n.quantiteEnStock||n.stock}),r.jsx(o,{variant:"caption",color:"text.secondary",children:"Réel"})]})}),r.jsx(ge,{align:"right",children:r.jsxs(a,{children:[r.jsx(o,{variant:"body2",fontWeight:"medium",children:n.coutAchatStockCDF?`${_a(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryAmount} ${_a(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryCurrency}`:`${_a(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryAmount} ${_a(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryCurrency}`}),r.jsx(o,{variant:"caption",color:"text.secondary",children:n.coutAchatStockCDF?`≈ $${_a(n.coutAchatStockCDF,B.tauxChangeUSDCDF).secondaryAmount} • Total`:`≈ $${_a(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).secondaryAmount} • Total`})]})}),r.jsx(ge,{align:"center",children:r.jsx(M,{icon:Y(i),label:K(i),color:G(i),size:"small"})}),r.jsx(ge,{children:(()=>{try{const e=new Date(n.dateModification);return isNaN(e.getTime())?"Date invalide":Ut(e,"dd/MM/yyyy",{locale:Da})}catch(e){return"Date invalide"}})()}),X.canManageProducts&&r.jsx(ge,{align:"center",children:r.jsxs(a,{display:"flex",gap:1,children:[r.jsx(F,{title:"Modifier",children:r.jsx(U,{size:"small",onClick:()=>Z(n),children:r.jsx(Ae,{fontSize:"small"})})}),ha.hasRole(["super_admin"])&&r.jsx(F,{title:"Supprimer",children:r.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le produit "${n.nom}" ?`)){const r=e.filter(e=>e.id!==n.id);t(r),await ma.setProducts(r),A("Produit supprimé avec succès"),setTimeout(()=>A(""),3e3)}})(n),children:r.jsx(q,{fontSize:"small"})})})]})})]},n.id)})})]}),r.jsx(Re,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:g,onPageChange:(e,t)=>{-1!==j&&y(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);C(t),y(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),r.jsxs(Me,{open:S,onClose:ae,maxWidth:"md",fullWidth:!0,children:[r.jsx(Ie,{children:b?"Modifier le Produit":"Nouveau Produit"}),r.jsxs(Ne,{children:[P&&r.jsx(O,{severity:"error",sx:{mb:2},children:P}),k&&r.jsx(O,{severity:"success",sx:{mb:2},children:k}),r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Nom du produit *",value:w.nom,onChange:e=>E({...w,nom:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:3,value:w.description,onChange:e=>E({...w,description:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ya,{label:"Prix d'achat *",value:w.prixAchatCDF,onChange:e=>E({...w,prixAchatCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixAchatCDF<=0,helperText:w.prixAchatCDF<=0?"Le prix d'achat doit être supérieur à zéro":"Prix d'achat du produit en CDF"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ya,{label:"Prix de vente *",value:w.prixCDF,onChange:e=>E({...w,prixCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixCDF<=w.prixAchatCDF,helperText:w.prixCDF<=w.prixAchatCDF?"Le prix de vente doit être supérieur au prix d'achat pour générer un bénéfice":"Prix de vente du produit en CDF"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Catégorie *"}),r.jsx(Ce,{value:w.categorie,label:"Catégorie *",onChange:e=>E({...w,categorie:e.target.value}),children:n.map(e=>r.jsx(J,{value:e.nom,children:e.nom},e.id))})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ka,{value:w.stock,onChange:e=>E({...w,stock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock actuel",helperText:"Quantité actuelle en stock"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ka,{value:w.stockMin,onChange:e=>E({...w,stockMin:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock minimum",helperText:"Seuil d'alerte pour stock bas"})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Gestion Avancée de l'Inventaire"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ka,{value:w.quantiteEnStock,onChange:e=>E({...w,quantiteEnStock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Quantité en Stock",helperText:"Quantité réelle en stock (éditable)"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ya,{label:"Coût d'Achat du Stock",value:w.coutAchatStockCDF,onChange:e=>E({...w,coutAchatStockCDF:e}),min:0,max:5e7,step:100,exchangeRate:B.tauxChangeUSDCDF,showSlider:!0,allowUSDInput:!0,helperText:"Coût total d'achat du stock actuel"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ya,{label:"Prix par Pièce",value:w.prixParPieceCDF,onChange:e=>E({...w,prixParPieceCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,showSlider:!0,allowUSDInput:!0,helperText:"Prix unitaire pour vente au détail"})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Valeur du Stock avec Bénéfice"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Valeur Stock avec Bénéfice (CDF)",value:(()=>{const e=(w.quantiteEnStock||0)*(w.prixParPieceCDF||w.prixCDF||0);return Ba(e,"CDF")})(),InputProps:{readOnly:!0,style:{backgroundColor:"#f5f5f5",fontWeight:"bold",color:"#1976d2"}},helperText:"Valeur totale du stock actuel avec marge bénéficiaire incluse",variant:"outlined"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Valeur Stock avec Bénéfice (USD)",value:(()=>{const e=(w.quantiteEnStock||0)*(w.prixParPieceCDF||w.prixCDF||0)/(B.tauxChangeUSDCDF||2800);return Ba(e,"USD")})(),InputProps:{readOnly:!0,style:{backgroundColor:"#f5f5f5",fontWeight:"bold",color:"#1976d2"}},helperText:"Équivalent en USD de la valeur du stock avec bénéfice",variant:"outlined"})})]})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:ae,children:"Annuler"}),r.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void T("Le nom du produit est requis");if(w.prixAchatCDF<=0)return void T("Le prix d'achat doit être supérieur à zéro");if(w.prixCDF<=0)return void T("Le prix de vente doit être supérieur à zéro");if(w.stock<0)return void T("Le stock ne peut pas être négatif");if(w.stockMin<0)return void T("Le stock minimum ne peut pas être négatif");if(!w.categorie)return void T("La catégorie est requise");const n=Oa(w.prixAchatCDF,w.prixCDF);if(!n.isValid)return void T(n.errorMessage||"Erreur de validation des prix");const r=await ma.getSettings(),a=(new Date).toISOString(),i=La(w.prixAchatCDF,w.prixCDF,r.tauxChangeUSDCDF);if(b){const n={...b,nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Ja(w.prixAchatCDF,r.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Ja(w.prixCDF,r.tauxChangeUSDCDF),beneficeUnitaireCDF:i.beneficeUnitaireCDF,beneficeUnitaireUSD:i.beneficeUnitaireUSD,categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,dateModification:a,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:Ja(w.coutAchatStockCDF,r.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:Ja(w.prixParPieceCDF,r.tauxChangeUSDCDF)},s=e.map(e=>e.id===b.id?n:e);t(s),await ma.setProducts(s),A("Produit mis à jour avec succès")}else{const n={id:Date.now().toString(),nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Ja(w.prixAchatCDF,r.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Ja(w.prixCDF,r.tauxChangeUSDCDF),beneficeUnitaireCDF:i.beneficeUnitaireCDF,beneficeUnitaireUSD:i.beneficeUnitaireUSD,codeQR:Wa(),categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,codeBarres:za(),dateCreation:a,dateModification:a,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:Ja(w.coutAchatStockCDF,r.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:Ja(w.prixParPieceCDF,r.tauxChangeUSDCDF)},s=[...e,n];t(s),await ma.setProducts(s),A("Produit créé avec succès")}setTimeout(()=>{ae()},1500)},variant:"contained",children:b?"Mettre à jour":"Créer"})]})]}),r.jsxs(Me,{open:R,onClose:()=>N(!1),maxWidth:"md",fullWidth:!0,children:[r.jsx(Ie,{children:"Importer des Produits depuis CSV"}),r.jsxs(Ne,{children:[r.jsx(Oe,{sx:{mb:2},children:"Collez le contenu CSV des produits à importer. Format attendu: ID, Nom du Produit, Description, Prix CDF, Prix USD, Code QR, Catégorie, Stock, Stock Minimum, Code Barres, Date de Création, Date de Modification"}),r.jsx(ne,{fullWidth:!0,multiline:!0,rows:10,value:L,onChange:e=>V(e.target.value),placeholder:"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\n1,iPhone 15,Smartphone Apple,2240000,800,SB123,Électronique,25,5,1234567890123,2024-01-01,2024-01-01",variant:"outlined"})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:()=>N(!1),children:"Annuler"}),r.jsx(I,{onClick:async()=>{if(L.trim())try{const n=Or.csvToArray(L,Vr),r=Or.validateCSVData(n,Vr);if(!r.isValid)return void T("Données CSV invalides: "+r.errors.join(", "));const a=n.map((e,t)=>({...e,id:e.id||Date.now().toString()+t,dateCreation:e.dateCreation||(new Date).toISOString(),dateModification:e.dateModification||(new Date).toISOString(),codeQR:e.codeQR||Wa(),codeBarres:e.codeBarres||za()})),i=new Set(e.map(e=>e.id)),s=a.filter(e=>!i.has(e.id)),o=[...e,...s];t(o),await ma.setProducts(o),A(`${s.length} produits importés avec succès`),N(!1),V(""),setTimeout(()=>A(""),3e3)}catch(n){T("Erreur lors de l'importation: "+n.message)}else T("Veuillez saisir le contenu CSV à importer")},variant:"contained",children:"Importer"})]})]})]})};class ei{static async generateSalesReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await ma.getSales()).filter(t=>{var n;return new Date(t.datevente).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RV-${e}`))}).length+1).toString().padStart(4,"0");return`RV-${e}-${t}`}static async generateExpenseReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await ma.getExpenses()).filter(t=>{var n;return new Date(t.dateDepense).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RD-${e}`))}).length+1).toString().padStart(4,"0");return`RD-${e}-${t}`}static async createSalesReceiptData(e){const t=await ma.getSettings();return{type:"sale",numero:e.numeroRecu||await this.generateSalesReceiptNumber(),date:e.datevente,entreprise:t.entreprise,sale:e,vendeur:e.vendeur}}static async createExpenseReceiptData(e){const t=await ma.getSettings();return{type:"expense",numero:e.numeroRecu||await this.generateExpenseReceiptNumber(),date:e.dateDepense,entreprise:t.entreprise,expense:e,creePar:e.creePar}}static getPaymentMethodLabel(e){switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}}static formatCurrency(e,t){const{formatCurrency:n}=require("@/utils/currencyUtils.js");return n(e,t)}static formatReceiptDate(e){return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}static async printReceipt(){return new Promise(e=>{try{if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)try{const{ipcRenderer:t}=window.require("electron"),n={silent:!1,printBackground:!0,color:!1,margin:{marginType:"none"},landscape:!1,pagesPerSheet:1,collate:!1,copies:1,header:"",footer:""};t.invoke("print-receipt",n).then(()=>{console.log("Receipt printed successfully via Electron IPC"),e()}).catch(t=>{console.error("Electron IPC print failed:",t),window.print(),e()})}catch(t){console.error("IPC not available, falling back to window.print():",t),window.print(),e()}else window.print(),e()}catch(fi){console.error("Error during printing:",fi),window.print(),e()}})}static async shouldAutoPrint(){var e;return(null==(e=(await ma.getSettings()).impression)?void 0:e.impressionAutomatique)||!1}static async getPaperSize(){var e;return(null==(e=(await ma.getSettings()).impression)?void 0:e.taillePapier)||"thermal"}}const ti=({receiptData:e,paperSize:t="thermal"})=>{const{sale:n,entreprise:i,numero:s,vendeur:l}=e;return r.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[r.jsxs(a,{textAlign:"center",mb:2,children:[i.logo&&r.jsx(a,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:r.jsx(a,{component:"img",src:i.logo,alt:`${i.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),r.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:i.nom}),r.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:i.adresse}),r.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",i.telephone]}),i.rccm&&r.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",i.rccm]}),i.idNat&&r.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",i.idNat]})]}),r.jsx(h,{sx:{my:1}}),r.jsxs(a,{textAlign:"center",mb:2,children:[r.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE VENTE #",s]}),r.jsx(o,{variant:"body2",children:ei.formatReceiptDate(n.datevente)})]}),r.jsx(a,{mb:1,children:r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Vendeur:"})," ",l]})}),r.jsxs(a,{mb:2,children:[r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Client:"})," ",n.nomClient]}),n.telephoneClient&&r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Téléphone:"})," ",n.telephoneClient]}),n.adresseClient&&r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Adresse:"})," ",n.adresseClient]})]}),r.jsx(h,{sx:{my:1}}),r.jsx(me,{children:r.jsxs(he,{size:"small",sx:{"& .MuiTableCell-root":{padding:"4px",border:"none"}},children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:r.jsx("strong",{children:"Produit"})}),r.jsx(ge,{align:"center",children:r.jsx("strong",{children:"Qté"})}),r.jsx(ge,{align:"right",children:r.jsx("strong",{children:"Prix unit."})}),r.jsx(ge,{align:"right",children:r.jsx("strong",{children:"Sous-total"})})]})}),r.jsx(ye,{children:n.produits.map((e,t)=>r.jsxs(xe,{children:[r.jsx(ge,{sx:{fontSize:"11px"},children:e.nomProduit}),r.jsx(ge,{align:"center",sx:{fontSize:"11px"},children:e.quantite}),r.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:ei.formatCurrency(e.prixUnitaireCDF,"CDF")}),r.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:ei.formatCurrency(e.totalCDF,"CDF")})]},t))})]})}),r.jsx(h,{sx:{my:1}}),r.jsx(a,{mb:2,children:r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Méthode de paiement:"})," ",ei.getPaymentMethodLabel(n.methodePaiement)]})}),r.jsxs(a,{mb:2,children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",children:[r.jsx(o,{variant:"body2",children:"Sous-total:"}),r.jsx(o,{variant:"body2",children:ei.formatCurrency(n.totalCDF,"CDF")})]}),r.jsxs(a,{display:"flex",justifyContent:"space-between",children:[r.jsx(o,{variant:"body1",fontWeight:"bold",children:"Total CDF:"}),r.jsx(o,{variant:"body1",fontWeight:"bold",children:ei.formatCurrency(n.totalCDF,"CDF")})]}),n.totalUSD&&r.jsxs(a,{display:"flex",justifyContent:"space-between",children:[r.jsx(o,{variant:"body2",children:"Total USD:"}),r.jsxs(o,{variant:"body2",children:["≈ ",ei.formatCurrency(n.totalUSD,"USD")]})]})]}),r.jsx(h,{sx:{my:1}}),n.notes&&r.jsx(a,{mb:2,children:r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Notes:"})," ",n.notes]})}),r.jsxs(a,{textAlign:"center",mt:2,children:[r.jsx(o,{variant:"body2",fontWeight:"bold",children:"Merci pour votre achat!"}),r.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},ni=({receiptData:e,paperSize:t="thermal"})=>{const{expense:n,entreprise:i,numero:s,creePar:l}=e;return r.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[r.jsxs(a,{textAlign:"center",mb:2,children:[i.logo&&r.jsx(a,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:r.jsx(a,{component:"img",src:i.logo,alt:`${i.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),r.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:i.nom}),r.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:i.adresse}),r.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",i.telephone]}),i.rccm&&r.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",i.rccm]}),i.idNat&&r.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",i.idNat]})]}),r.jsx(h,{sx:{my:1}}),r.jsxs(a,{textAlign:"center",mb:2,children:[r.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE DÉPENSE #",s]}),r.jsx(o,{variant:"body2",children:ei.formatReceiptDate(n.dateDepense)})]}),r.jsx(a,{mb:1,children:r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Créé par:"})," ",l]})}),r.jsx(h,{sx:{my:1}}),r.jsxs(a,{mb:2,children:[r.jsx(o,{variant:"body2",mb:1,children:r.jsx("strong",{children:"Description:"})}),r.jsx(o,{variant:"body2",sx:{pl:1,mb:2},children:n.description}),r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Catégorie:"})," ",n.categorie]})]}),r.jsx(h,{sx:{my:1}}),r.jsxs(a,{mb:2,children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",mb:1,children:[r.jsx(o,{variant:"body1",fontWeight:"bold",children:"Montant:"}),r.jsx(o,{variant:"body1",fontWeight:"bold",children:ei.formatCurrency(n.montantCDF,"CDF")})]}),n.montantUSD&&r.jsxs(a,{display:"flex",justifyContent:"space-between",children:[r.jsx(o,{variant:"body2",children:"Équivalent USD:"}),r.jsxs(o,{variant:"body2",children:["≈ ",ei.formatCurrency(n.montantUSD,"USD")]})]})]}),r.jsx(h,{sx:{my:1}}),n.notes&&r.jsxs(a,{mb:2,children:[r.jsx(o,{variant:"body2",mb:1,children:r.jsx("strong",{children:"Notes:"})}),r.jsx(o,{variant:"body2",sx:{pl:1},children:n.notes})]}),r.jsxs(a,{textAlign:"center",mt:2,children:[r.jsx(o,{variant:"body2",fontWeight:"bold",children:"Reçu de dépense validé"}),r.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},ri=({open:e,onClose:t,receiptData:n,paperSize:i="thermal",onPrintSuccess:s})=>{const[l,c]=vt.useState(!1);return n?r.jsxs(Me,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,className:"receipt-preview-modal",PaperProps:{sx:{maxHeight:"90vh","@media print":{boxShadow:"none",margin:0,maxWidth:"none",maxHeight:"none"}}},children:[r.jsxs(Ie,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center","@media print":{display:"none"}},children:[r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(se,{}),r.jsx(o,{variant:"h6",children:"Aperçu du reçu"})]}),r.jsx(U,{onClick:t,size:"small",children:r.jsx(Ve,{})})]}),r.jsx(Ne,{sx:{p:0,"@media print":{padding:0,margin:0}},children:r.jsx(a,{sx:{p:2,"@media print":{padding:0,margin:0}},children:"sale"===n.type?r.jsx(ti,{receiptData:n,paperSize:i}):r.jsx(ni,{receiptData:n,paperSize:i})})}),r.jsxs(Le,{sx:{p:2,gap:1,"@media print":{display:"none"}},children:[r.jsx(I,{onClick:t,variant:"outlined",disabled:l,children:"Fermer"}),r.jsx(I,{onClick:async()=>{if(n){c(!0);try{const e=document.createElement("div");e.className="receipt-print-container",e.style.cssText="\n        position: fixed;\n        top: -9999px;\n        left: -9999px;\n        width: 100%;\n        height: auto;\n        background: white;\n        z-index: 9999;\n      ";const n=document.querySelector(".receipt-container");if(n){const t=n.cloneNode(!0);t.style.cssText="\n          display: block !important;\n          position: static !important;\n          width: 100% !important;\n          height: auto !important;\n          margin: 0 !important;\n          padding: 5mm !important;\n          background: white !important;\n          color: black !important;\n        ",e.appendChild(t)}document.body.appendChild(e),await new Promise(e=>setTimeout(e,200));const r=document.querySelector(".receipt-preview-modal");r&&(r.style.display="none"),await ei.printReceipt(),document.body.removeChild(e),r&&(r.style.display="block"),s&&s(),setTimeout(()=>{t()},500)}catch(fi){console.error("Error printing receipt:",fi)}finally{c(!1)}}},variant:"contained",startIcon:l?r.jsx(qe,{size:16}):r.jsx(Be,{}),disabled:l,children:l?"Impression...":"Imprimer"})]})]}):null},ai=()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(""),[m,g]=vt.useState(0),[j,D]=vt.useState(10),[v,b]=vt.useState(!1),[w,E]=vt.useState(!1),[P,T]=vt.useState(null),[k,A]=vt.useState(""),[R,N]=vt.useState(""),[L,q]=vt.useState([]),[B,_]=vt.useState(null),[$,z]=vt.useState(1),[W,X]=vt.useState(""),[Q,H]=vt.useState(""),[G,Y]=vt.useState(""),[K,Z]=vt.useState("cash"),[ae,ie]=vt.useState("cash"),[se,oe]=vt.useState("CDF"),[le,de]=vt.useState(""),[ue,Se]=vt.useState(""),[be,fe]=vt.useState(!1),[we,Ee]=vt.useState({tauxChangeUSDCDF:2800}),[Ue,Te]=vt.useState(!1),[ke,Ae]=vt.useState(null),[Oe,Ve]=vt.useState(!1),Be=ha.getUserPermissions(),Ge=ha.getCurrentUser(),Ye=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};vt.useEffect(()=>{Ke()},[]),vt.useEffect(()=>{Ze()},[e,d]),vt.useEffect(()=>{-1===j&&D(s.length||1)},[s.length,j]);const Ke=async()=>{const e=await ma.getSales(),n=await ma.getProducts(),r=await ma.getSettings();t(e),i(n),Ee(r)},Ze=()=>{let t=e;d&&(t=t.filter(e=>e.nomClient.toLowerCase().includes(d.toLowerCase())||e.id.toLowerCase().includes(d.toLowerCase())||e.vendeur.toLowerCase().includes(d.toLowerCase()))),l(t)},et=()=>{b(!1),tt(),A(""),N("")},tt=()=>{q([]),_(null),z(1),X(""),H(""),Y(""),Z("cash"),ie("cash"),oe("CDF"),de(""),Se(""),fe(!1)},nt=e=>{const t=L.filter((t,n)=>n!==e);q(t)},rt=()=>L.reduce((e,t)=>({CDF:Ua(e.CDF+t.totalCDF),USD:Ua(e.USD+(t.totalUSD||0))}),{CDF:0,USD:0}),at=e=>"USD"===se?{price:e.prixUSD||e.prixCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),currency:"USD",symbol:"$"}:{price:e.prixCDF,currency:"CDF",symbol:""},it=e=>{const t=at(e);return`${t.symbol}${t.price.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===t.currency?2:0,maximumFractionDigits:"USD"===t.currency?2:0})} ${t.currency}`},st=e=>{T(e),E(!0)},ot=e=>{switch(e){case"cash":default:return r.jsx(f,{});case"banque":return r.jsx($e,{});case"mobile_money":return r.jsx(He,{})}},lt=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},ct=e.filter(e=>{const t=Ye(e.datevente);if(!t)return!1;const n=new Date;return t.toDateString()===n.toDateString()}),dt=ct.reduce((e,t)=>e+t.totalCDF,0),ut=e.length,mt=e.reduce((e,t)=>e+t.totalCDF,0),ht=ut>0?mt/ut:0;return r.jsxs(a,{children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[r.jsx(o,{variant:"h4",children:"Gestion des Ventes"}),Be.canManageSales&&r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>{tt(),b(!0),A(""),N("")},children:"Nouvelle Vente"})]}),R&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>N(""),children:R}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),r.jsx(o,{variant:"h6",children:ct.length}),r.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_a(dt,we.tauxChangeUSDCDF).primaryAmount," ",_a(dt,we.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(dt,we.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Ventes"}),r.jsx(o,{variant:"h6",children:ut}),r.jsxs(o,{variant:"body2",color:"success.main",fontWeight:"medium",children:[_a(mt,we.tauxChangeUSDCDF).primaryAmount," ",_a(mt,we.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(mt,we.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(_e,{color:"success",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Vente Moyenne"}),r.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_a(ht,we.tauxChangeUSDCDF).primaryAmount," ",_a(ht,we.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(ht,we.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(S,{color:"info",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes à Crédit"}),r.jsx(o,{variant:"h6",children:e.filter(e=>"credit"===e.typeVente).length})]}),r.jsx($e,{color:"warning",sx:{fontSize:40}})]})})})})]}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, ID vente ou vendeur...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Fe,{})})}})}),r.jsxs(me,{component:c,children:[r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Produits Vendus"}),r.jsx(ge,{children:"Client"}),r.jsx(ge,{children:"Vendeur"}),r.jsx(ge,{align:"right",children:"Total CDF"}),r.jsx(ge,{align:"right",children:"Total USD"}),r.jsx(ge,{align:"center",children:"Paiement"}),r.jsx(ge,{align:"center",children:"Type"}),r.jsx(ge,{children:"Date"}),r.jsx(ge,{align:"center",children:"Actions"})]})}),r.jsx(ye,{children:(-1===j?s:s.slice(m*j,m*j+j)).map(e=>r.jsxs(xe,{hover:!0,onClick:()=>st(e),sx:{cursor:"pointer"},children:[r.jsx(ge,{children:r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",color:"primary",gutterBottom:!0,children:e.produits.map(e=>e.nomProduit).join(", ")}),r.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["ID: ",e.id," • ",e.produits.length," article",e.produits.length>1?"s":""]})]})}),r.jsx(ge,{children:r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",children:e.nomClient}),e.telephoneClient&&r.jsx(o,{variant:"caption",color:"text.secondary",children:e.telephoneClient})]})}),r.jsx(ge,{children:e.vendeur}),r.jsx(ge,{align:"right",children:Ba(e.totalCDF,"CDF")}),r.jsx(ge,{align:"right",children:e.totalUSD?Ba(e.totalUSD,"USD"):"-"}),r.jsx(ge,{align:"center",children:r.jsx(M,{icon:ot(e.methodePaiement),label:lt(e.methodePaiement),size:"small"})}),r.jsx(ge,{align:"center",children:r.jsx(M,{label:"cash"===e.typeVente?"Cash":"Crédit",color:"cash"===e.typeVente?"success":"warning",size:"small"})}),r.jsx(ge,{children:(()=>{const t=Ye(e.datevente);return t?Ut(t,"dd/MM/yyyy HH:mm",{locale:Da}):"Date invalide"})()}),r.jsx(ge,{align:"center",children:r.jsx(F,{title:"Voir détails",children:r.jsx(U,{size:"small",onClick:()=>st(e),children:r.jsx(f,{fontSize:"small"})})})})]},e.id))})]}),r.jsx(Re,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:m,onPageChange:(e,t)=>{-1!==j&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);D(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),r.jsxs(Me,{open:v,onClose:et,maxWidth:"lg",fullWidth:!0,children:[r.jsx(Ie,{children:"Nouvelle Vente"}),r.jsxs(Ne,{children:[k&&r.jsx(O,{severity:"error",sx:{mb:2},children:k}),R&&r.jsx(O,{severity:"success",sx:{mb:2},children:R}),r.jsxs(a,{sx:{mb:3,p:2,bgcolor:"primary.50",borderRadius:1,border:"1px solid",borderColor:"primary.200"},children:[r.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:"bold",color:"primary.main"},children:"💱 Devise de la Vente"}),r.jsxs(je,{fullWidth:!0,size:"small",children:[r.jsx(De,{children:"Sélectionnez la devise pour cette vente"}),r.jsxs(Ce,{value:se,label:"Sélectionnez la devise pour cette vente",onChange:e=>oe(e.target.value),children:[r.jsx(J,{value:"CDF",children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(o,{children:"🇨🇩"}),r.jsx(o,{children:"CDF (Franc Congolais) - Devise principale"})]})}),r.jsx(J,{value:"USD",children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(o,{children:"🇺🇸"}),r.jsx(o,{children:"USD (Dollar Américain)"})]})})]})]}),r.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Les prix des produits seront affichés dans la devise sélectionnée"})]}),r.jsxs(ce,{container:!0,spacing:3,children:[r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Sélection des Produits"}),r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ze,{options:n.filter(e=>e.stock>0),getOptionLabel:e=>`${e.nom} - ${it(e)} - Stock: ${e.stock}`,value:B,onChange:(e,t)=>_(t),renderInput:e=>r.jsx(ne,{...e,label:"Produit",fullWidth:!0}),renderOption:(e,t)=>r.jsx(a,{component:"li",...e,children:r.jsxs(a,{sx:{display:"flex",flexDirection:"column",width:"100%"},children:[r.jsx(o,{variant:"body1",sx:{fontWeight:500},children:t.nom}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["Prix: ",it(t)," • Stock: ",t.stock," • Code: ",t.codeQR]})]})})})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(a,{children:[r.jsx(Ka,{value:$,onChange:z,min:1,max:(null==B?void 0:B.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0,label:"Quantité"}),B&&r.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",sx:{mt:1},children:["Stock disponible: ",B.stock]})]})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsx(I,{fullWidth:!0,variant:"contained",onClick:()=>{if(!B)return void A("Veuillez sélectionner un produit");if($<=0)return void A("La quantité doit être supérieure à 0");if($>B.stock)return void A("Quantité insuffisante en stock");const e=L.findIndex(e=>e.produitId===B.id);if(e>=0){const t=[...L],n=t[e].quantite+$;if(n>B.stock)return void A("Quantité totale dépasse le stock disponible");t[e]={...t[e],quantite:n,totalCDF:n*B.prixCDF,totalUSD:B.prixUSD?n*B.prixUSD:void 0},q(t)}else{const e={produitId:B.id,nomProduit:B.nom,quantite:$,prixUnitaireCDF:B.prixCDF,prixUnitaireUSD:B.prixUSD,totalCDF:$*B.prixCDF,totalUSD:B.prixUSD?$*B.prixUSD:void 0};q([...L,e])}_(null),z(1),A("")},disabled:!B,children:"Ajouter"})})]}),B&&r.jsx(a,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Produit sélectionné: ",B.nom]}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["Code: ",B.codeQR," • Catégorie: ",B.categorie]})]}),r.jsxs(ce,{item:!0,xs:12,md:3,children:[r.jsxs(o,{variant:"subtitle2",color:"primary",sx:{fontWeight:"bold"},children:["Prix unitaire: ",it(B)]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["Devise: ",se]})]}),r.jsxs(ce,{item:!0,xs:12,md:3,children:[r.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Total: ",(()=>{const e=at(B),t=e.price*$;return`${e.symbol}${t.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===e.currency?2:0,maximumFractionDigits:"USD"===e.currency?2:0})} ${e.currency}`})()]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:[$," × ",it(B)]})]})]})})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsxs(o,{variant:"h6",gutterBottom:!0,children:["Panier (",L.length," articles) - Devise: ",se]}),0===L.length?r.jsx(O,{severity:"info",children:"Aucun produit ajouté"}):r.jsxs(p,{children:[L.map((e,t)=>{var i;const s=(e=>{if("USD"===se){const t=e.prixUnitaireUSD||e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),n=e.totalUSD||e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800);return{unitPrice:`$${t.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`,total:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`}}return{unitPrice:`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF`,total:`${e.totalCDF.toLocaleString("fr-FR")} CDF`}})(e);return r.jsxs(ft.Fragment,{children:[r.jsxs(x,{children:[r.jsx(y,{primary:r.jsx(o,{variant:"subtitle1",sx:{fontWeight:500},children:e.nomProduit}),secondary:`Prix unitaire: ${s.unitPrice}`}),r.jsx(V,{children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(Ka,{value:e.quantite,onChange:e=>((e,t)=>{if(t<=0)return void nt(e);const r=L[e],a=n.find(e=>e.id===r.produitId);if(a&&t>a.stock)return void A(`Quantité maximale disponible: ${a.stock}`);const i=[...L];i[e]={...r,quantite:t,totalCDF:t*r.prixUnitaireCDF,totalUSD:r.prixUnitaireUSD?t*r.prixUnitaireUSD:void 0},q(i),A("")})(t,e),min:1,max:(null==(i=n.find(t=>t.id===e.produitId))?void 0:i.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0}),r.jsx(o,{variant:"subtitle1",sx:{minWidth:"120px",textAlign:"right",fontWeight:"bold",color:"primary.main"},children:s.total}),r.jsx(U,{size:"small",color:"error",onClick:()=>nt(t),title:"Supprimer l'article",children:r.jsx(_e,{fontSize:"small"})})]})})]}),t<L.length-1&&r.jsx(h,{})]},t)}),r.jsx(h,{sx:{my:2}}),r.jsx(x,{sx:{bgcolor:"primary.50",borderRadius:1},children:r.jsx(y,{primary:r.jsxs(o,{variant:"h6",sx:{fontWeight:"bold",color:"primary.main"},children:["Total: ",(()=>{const e=rt();return"USD"===se?`$${e.USD.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`:`${e.CDF.toLocaleString("fr-FR")} CDF`})()]}),secondary:r.jsx(o,{variant:"body2",color:"text.secondary",children:"USD"===se?`≈ ${Ba(rt().CDF,"CDF")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`:`≈ ${Ba(rt().USD,"USD")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`})})})]})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations Client"}),r.jsxs(ce,{container:!0,spacing:2,children:[r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,label:"credit"===ae?"Nom du client *":"Nom du client",placeholder:"credit"===ae?"Nom requis pour crédit":"Client (par défaut)",value:W,onChange:e=>X(e.target.value),required:"credit"===ae,error:"credit"===ae&&!W.trim()&&k.includes("nom du client"),helperText:"credit"===ae&&!W.trim()&&k.includes("nom du client")?"Le nom du client est obligatoire pour les ventes à crédit":""})}),r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,label:"Téléphone",value:Q,onChange:e=>H(e.target.value)})}),r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,label:"Adresse",value:G,onChange:e=>Y(e.target.value)})})]})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations de Paiement"}),r.jsxs(ce,{container:!0,spacing:2,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(je,{component:"fieldset",children:[r.jsx(ve,{component:"legend",children:"Type de vente"}),r.jsxs(We,{row:!0,value:ae,onChange:e=>ie(e.target.value),children:[r.jsx(Xe,{value:"cash",control:r.jsx(Qe,{}),label:"Cash"}),r.jsx(Xe,{value:"credit",control:r.jsx(Qe,{}),label:"Crédit"})]})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Méthode de paiement"}),r.jsxs(Ce,{value:K,label:"Méthode de paiement",onChange:e=>Z(e.target.value),children:[r.jsx(J,{value:"cash",children:"Cash"}),r.jsx(J,{value:"banque",children:"Banque"}),r.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),"credit"===ae&&r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Date d'échéance",type:"date",value:le,onChange:e=>de(e.target.value),InputLabelProps:{shrink:!0}})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:ue,onChange:e=>Se(e.target.value)})})]})]})]})]}),r.jsxs(Le,{children:[r.jsx(Xe,{control:r.jsx(Je,{checked:be,onChange:e=>fe(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),Oe&&r.jsxs(a,{display:"flex",alignItems:"center",gap:1,mr:2,children:[r.jsx(qe,{size:20}),r.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),r.jsx(I,{onClick:et,children:"Annuler"}),r.jsx(I,{onClick:async()=>{var r;if(0===L.length)return void A("Veuillez ajouter au moins un produit");if("credit"===ae&&!W.trim())return void A("Le nom du client est obligatoire pour les ventes à crédit");if("credit"===ae&&!le)return void A("La date d'échéance est requise pour les ventes à crédit");const a=rt(),s=(new Date).toISOString(),o=be||(null==(r=null==we?void 0:we.impression)?void 0:r.impressionAutomatique)||!1,l=o?await ei.generateSalesReceiptNumber():void 0,c={id:`VTE-${Date.now()}`,produits:L,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:G.trim()||void 0,totalCDF:a.CDF,totalUSD:a.USD,methodePaiement:K,typeVente:ae,datevente:s,vendeur:(null==Ge?void 0:Ge.nom)||"Inconnu",notes:ue.trim()||void 0,numeroRecu:l},d=n.map(e=>{const t=L.find(t=>t.produitId===e.id);return t?{...e,stock:e.stock-t.quantite,dateModification:s}:e}),u=[...e,c];if(t(u),i(d),await ma.setSales(u),await ma.setProducts(d),"credit"===ae){const e={id:`DET-${Date.now()}`,venteId:c.id,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:G.trim()||void 0,montantTotalCDF:a.CDF,montantTotalUSD:a.USD,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:a.CDF,montantRestantUSD:a.USD,dateCreation:s,dateEcheance:le,statut:"active",statutPaiement:"impaye",paiements:[],notes:ue.trim()||void 0,deviseVente:se},t=await ma.getDebts();await ma.setDebts([...t,e])}if(N("Vente enregistrée avec succès"),o)try{Ve(!0);const e=await ei.createSalesReceiptData(c);Ae(e),Te(!0)}catch(m){console.error("Erreur lors de la génération du reçu:",m),A("Erreur lors de la génération du reçu")}finally{Ve(!1)}setTimeout(()=>{et()},1500)},variant:"contained",disabled:0===L.length||Oe,children:"Enregistrer la Vente"})]})]}),r.jsxs(Me,{open:w,onClose:()=>E(!1),maxWidth:"md",fullWidth:!0,children:[r.jsx(Ie,{children:"Détails de la Vente"}),r.jsx(Ne,{children:P&&r.jsxs(ce,{container:!0,spacing:2,children:[r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Vendus:"}),r.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:P.produits.map(e=>e.nomProduit).join(", ")})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),r.jsx(o,{variant:"body2",color:"text.secondary",children:P.id})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Date:"}),r.jsx(o,{variant:"body1",children:Ut(new Date(P.datevente),"dd/MM/yyyy HH:mm",{locale:Da})})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Client:"}),r.jsx(o,{variant:"body1",children:P.nomClient}),P.telephoneClient&&r.jsx(o,{variant:"body2",color:"text.secondary",children:P.telephoneClient})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Vendeur:"}),r.jsx(o,{variant:"body1",children:P.vendeur})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Produits:"}),r.jsxs(p,{dense:!0,children:[P.produits.map((e,t)=>r.jsxs(x,{children:[r.jsx(y,{primary:e.nomProduit,secondary:r.jsxs(a,{children:[r.jsxs(o,{variant:"body2",component:"span",children:[e.quantite," × ",Ba(e.prixUnitaireCDF,"CDF")]}),r.jsxs(o,{variant:"caption",display:"block",color:"text.secondary",children:["≈ ",e.quantite," × ",Ba(e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})}),r.jsxs(a,{textAlign:"right",children:[r.jsx(o,{variant:"body2",fontWeight:"medium",children:Ba(e.totalCDF,"CDF")}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ ",Ba(e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})]},t)),r.jsx(h,{}),r.jsx(x,{children:r.jsx(y,{primary:r.jsxs(a,{children:[r.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:["Total: ",_a(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryAmount," ",_a(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_a(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).secondaryAmount]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["Taux de change: 1 USD = ",(null==we?void 0:we.tauxChangeUSDCDF)||2800," CDF"]})]})})})]})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Méthode de paiement:"}),r.jsx(M,{icon:ot(P.methodePaiement),label:lt(P.methodePaiement)})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Type de vente:"}),r.jsx(M,{label:"cash"===P.typeVente?"Cash":"Crédit",color:"cash"===P.typeVente?"success":"warning"})]}),P.notes&&r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"subtitle2",children:"Notes:"}),r.jsx(o,{variant:"body1",children:P.notes})]})]})}),r.jsx(Le,{children:r.jsx(I,{onClick:()=>E(!1),children:"Fermer"})})]}),r.jsx(ri,{open:Ue,onClose:()=>Te(!1),receiptData:ke,onPrintSuccess:()=>{N("Reçu imprimé avec succès"),setTimeout(()=>N(""),3e3)}})]})},ii=()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState([]),[s,l]=vt.useState(""),[d,u]=vt.useState("all"),[m,g]=vt.useState("all"),[j,D]=vt.useState(0),[C,v]=vt.useState(10),[b,w]=vt.useState(!1),[E,P]=vt.useState(!1),[T,k]=vt.useState(null),[A,R]=vt.useState(0),[N,L]=vt.useState("cash"),[V,q]=vt.useState(""),[B,$]=vt.useState(""),[W,X]=vt.useState(""),[Q,H]=vt.useState({tauxChangeUSDCDF:2800}),[Y,K]=vt.useState([]),[Z,ae]=vt.useState(!1),[ie,oe]=vt.useState("CDF"),de=ha.getUserPermissions(),ue=e=>Y.find(t=>t.id===e.venteId),Se=e=>{const t=ue(e);return t&&t.produits&&0!==t.produits.length?t.produits.map(e=>`${e.nomProduit} (x${e.quantite})`).join(", "):"Produits non disponibles"},ve=e=>{if(e.nomClient&&""!==e.nomClient.trim()&&"Client"!==e.nomClient)return e.nomClient;const t=ue(e);return t&&t.nomClient&&""!==t.nomClient.trim()&&"Client"!==t.nomClient?t.nomClient:"Client"},be=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};vt.useEffect(()=>{fe();(async()=>{0===(await ma.getDebts()).length&&(console.log("No debts found, forcing initialization..."),await ma.forceInitializeDebts(),setTimeout(()=>fe(),500))})()},[]),vt.useEffect(()=>{we()},[e,s,d,m]),vt.useEffect(()=>{-1===C&&v(n.length||1)},[n.length,C]);const fe=async()=>{try{const e=await ma.getDebts(),n=await ma.getSettings(),r=await ma.getSales(),a=e.filter(e=>e.id&&e.venteId?(e.nomClient&&""!==e.nomClient.trim()||(e.nomClient="Client"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,!0):(console.warn("Invalid debt record missing required fields:",e),!1)).map(e=>{try{const t=We(e);if("paid"===t.statut)return t;const n=new Date,r=be(t.dateEcheance);return t.montantRestantCDF<=0?{...t,statut:"paid",statutPaiement:"paye"}:r&&Rt(n,r)?{...t,statut:"overdue"}:{...t,statut:"active"}}catch(t){return console.warn("Error processing debt status:",e,t),e}});t(a),H(n),K(r),await ma.setDebts(a)}catch(e){console.error("Error loading debt data:",e),$("Erreur lors du chargement des données de dette.")}},we=()=>{try{let t=e;if(s&&s.trim()){const e=s.toLowerCase().trim();t=t.filter(t=>{var n,r,a,i;try{const s=(null==(n=t.nomClient)?void 0:n.toLowerCase())||"",o=(null==(r=t.id)?void 0:r.toLowerCase())||"",l=(null==(a=t.venteId)?void 0:a.toLowerCase())||"",c=(null==(i=t.telephoneClient)?void 0:i.toLowerCase())||"",d=Se(t).toLowerCase();return s.includes(e)||o.includes(e)||l.includes(e)||c.includes(e)||d.includes(e)}catch(s){return console.warn("Error filtering debt:",t,s),!1}})}"all"!==d&&(t=t.filter(e=>e.statut===d)),"all"!==m&&(t=t.filter(e=>e.statutPaiement===m)),i(t)}catch(t){console.error("Error in filterDebts:",t),i(e),$("Erreur lors de la recherche. Affichage de toutes les dettes.")}},Ee=e=>{switch(e){case"active":return"primary";case"overdue":return"error";case"paid":return"success";default:return"default"}},Pe=e=>{switch(e){case"active":return"Active";case"overdue":return"En retard";case"paid":return"Payée";default:return e}},Te=e=>{switch(e){case"active":return r.jsx(Ge,{});case"overdue":return r.jsx(z,{});case"paid":return r.jsx(_,{});default:return r.jsx(S,{})}},ke=e=>{switch(e){case"paye":return"Payé";case"impaye":return"Impayé";default:return e}},Ae=e=>{switch(e){case"paye":return r.jsx(Ye,{});case"impaye":return r.jsx(et,{});default:return r.jsx(S,{})}},Oe=()=>{w(!1),k(null),R(0),L("cash"),q(""),oe("CDF"),$(""),X("")},Ve=e=>{k(e),P(!0)},qe=()=>{P(!1),k(null),ae(!1),R(0),q(""),oe("CDF"),$(""),X("")},Be=async()=>{if(!T)return;if(A<=0)return void $("Le montant doit être supérieur à 0");if(A>T.montantRestantCDF)return void $("Le montant ne peut pas dépasser le montant restant");const n=(new Date).toISOString();let r,a;"USD"===ie?(a=A/Q.tauxChangeUSDCDF,r=A):(r=A,a=A/Q.tauxChangeUSDCDF);const i={id:`PAY-${Date.now()}`,montantCDF:r,montantUSD:a,methodePaiement:N,datePaiement:n,notes:V.trim()||void 0,deviseOriginale:ie},s={...T,montantPayeCDF:T.montantPayeCDF+A,montantPayeUSD:(T.montantPayeUSD||0)+A/Q.tauxChangeUSDCDF,paiements:[...T.paiements,i]},o=We(s),l={...o,statut:o.montantRestantCDF<=0?"paid":T.statut,statutPaiement:Je(o)},c=e.map(e=>e.id===T.id?l:e);t(c),await ma.setDebts(c),k(l),X("Paiement enregistré avec succès"),Z?setTimeout(()=>{ae(!1),R(0),q(""),oe("CDF"),$(""),X("")},2e3):setTimeout(()=>{Oe()},1500)},_e=e=>{switch(e){case"cash":default:return r.jsx(f,{});case"banque":return r.jsx($e,{});case"mobile_money":return r.jsx(He,{})}},ze=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},We=e=>{const t=e.paiements.reduce((e,t)=>e+t.montantCDF,0),n=e.paiements.reduce((e,t)=>e+(t.montantUSD||0),0),r=e.paiements.length>0?t:e.montantPayeCDF,a=e.paiements.length>0?n:e.montantPayeUSD||0,i=e.montantTotalCDF-r,s=e.montantTotalUSD?e.montantTotalUSD-a:void 0,o=e.montantTotalUSD||e.montantTotalCDF/Q.tauxChangeUSDCDF,l=a||r/Q.tauxChangeUSDCDF,c=void 0!==s?Math.max(0,s):Math.max(0,i/Q.tauxChangeUSDCDF);return{...e,montantTotalCDF:e.montantTotalCDF,montantTotalUSD:o,montantPayeCDF:r,montantPayeUSD:l,montantRestantCDF:Math.max(0,i),montantRestantUSD:c}},Qe=e=>0===e.montantTotalCDF?100:Math.min(100,e.montantPayeCDF/e.montantTotalCDF*100),Je=e=>e.montantPayeCDF>=e.montantTotalCDF?"paye":"impaye",tt=e=>"paye"===Je(e)?"Payé":"Impayé",nt=e=>"paye"===Je(e)?"success":"error",rt=e=>"paye"===Je(e)?r.jsx(_,{fontSize:"small"}):r.jsx(Ze,{fontSize:"small"}),at=e.filter(e=>"active"===e.statut),it=e.filter(e=>"overdue"===e.statut),st=e.filter(e=>"paye"===e.statutPaiement),ot=e.filter(e=>"impaye"===e.statutPaiement),lt=e.reduce((e,t)=>e+t.montantTotalCDF,0),ct=e.reduce((e,t)=>e+t.montantRestantCDF,0);return r.jsxs(a,{children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[r.jsx(o,{variant:"h4",children:"Gestion des Dettes"}),r.jsx(I,{variant:"contained",startIcon:r.jsx(Ue,{}),onClick:()=>{const e=`SmartBoutique_Dettes_${(new Date).toISOString().split("T")[0]}.csv`;Or.downloadCSV(n,_r,e),X("Dettes exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>X(""),3e3)},children:"Exporter les Dettes"})]}),W&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>X(""),children:W}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dettes Actives"}),r.jsx(o,{variant:"h6",children:at.length}),r.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_a(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",_a(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(Ge,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Retard"}),r.jsx(o,{variant:"h6",color:"error.main",children:it.length}),r.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_a(it.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",_a(it.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(it.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(z,{color:"error",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Statut Payé"}),r.jsx(o,{variant:"h6",color:"success.main",children:st.length}),r.jsxs(o,{variant:"body2",color:"error",children:["Impayé: ",ot.length]})]}),r.jsx(Ye,{color:"success",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Restant"}),r.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_a(ct,Q.tauxChangeUSDCDF).primaryAmount," ",_a(ct,Q.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_a(ct,Q.tauxChangeUSDCDF).secondaryAmount," sur ",_a(lt,Q.tauxChangeUSDCDF).primaryAmount," ",_a(lt,Q.tauxChangeUSDCDF).primaryCurrency]})]}),r.jsx(S,{color:"warning",sx:{fontSize:40}})]})})})})]}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, produit, ID dette ou ID vente...",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Fe,{})})}})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Statut"}),r.jsxs(Ce,{value:d,label:"Statut",onChange:e=>u(e.target.value),children:[r.jsx(J,{value:"all",children:"Tous"}),r.jsx(J,{value:"active",children:"Actives"}),r.jsx(J,{value:"overdue",children:"En retard"}),r.jsx(J,{value:"paid",children:"Payées"})]})]})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Statut Paiement"}),r.jsxs(Ce,{value:m,label:"Statut Paiement",onChange:e=>g(e.target.value),children:[r.jsx(J,{value:"all",children:"Tous"}),r.jsx(J,{value:"paye",children:"Payé"}),r.jsx(J,{value:"impaye",children:"Impayé"})]})]})})]})}),r.jsxs(me,{component:c,children:[r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Client"}),r.jsx(ge,{children:"Montant Dû"}),r.jsx(ge,{align:"right",children:"Payé"}),r.jsx(ge,{align:"right",children:"Restant"}),r.jsx(ge,{align:"center",children:"Progression"}),r.jsx(ge,{align:"center",children:"Statut"}),r.jsx(ge,{align:"center",children:"Statut Paiement"}),r.jsx(ge,{children:"Échéance"}),de.canManageDebts&&r.jsx(ge,{align:"center",children:"Actions"})]})}),r.jsx(ye,{children:(-1===C?n:n.slice(j*C,j*C+C)).map(e=>{try{const t=Qe(e),n="overdue"===e.statut;return r.jsxs(xe,{hover:!0,onClick:()=>Ve(e),sx:{cursor:"pointer"},children:[r.jsx(ge,{children:r.jsxs(a,{children:[r.jsx(o,{variant:"h6",fontWeight:"bold",color:"primary",gutterBottom:!0,children:ve(e)}),e.telephoneClient&&r.jsxs(o,{variant:"body2",color:"text.secondary",display:"block",sx:{mb:.5},children:["📞 ",e.telephoneClient]}),r.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",display:"block",sx:{mb:.5},children:["🛍️ Produits: ",Se(e)]}),r.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["Créé le: ",(()=>{try{const t=be(e.dateCreation);return t?Ut(t,"dd/MM/yyyy",{locale:Da}):"Date invalide"}catch(t){return console.warn("Error formatting creation date:",e.dateCreation,t),"Date invalide"}})()," • ID: ",e.venteId||"N/A"]})]})}),r.jsx(ge,{align:"right",children:Ba(e.montantTotalCDF,"CDF")}),r.jsx(ge,{align:"right",children:Ba(e.montantPayeCDF,"CDF")}),r.jsx(ge,{align:"right",children:r.jsx(o,{variant:"body2",color:e.montantRestantCDF>0?"error":"success.main",children:Ba(e.montantRestantCDF,"CDF")})}),r.jsx(ge,{align:"center",children:r.jsxs(a,{sx:{width:100},children:[r.jsx(le,{variant:"determinate",value:t,color:100===t?"success":n?"error":"primary"}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(t),"%"]})]})}),r.jsx(ge,{align:"center",children:r.jsx(M,{icon:Te(e.statut),label:Pe(e.statut),color:Ee(e.statut),size:"small"})}),r.jsx(ge,{align:"center",children:r.jsx(M,{icon:rt(e),label:tt(e),color:nt(e),size:"small"})}),r.jsx(ge,{children:r.jsx(o,{variant:"body2",color:n?"error":"text.primary",children:(()=>{const t=be(e.dateEcheance);return t?Ut(t,"dd/MM/yyyy",{locale:Da}):"Date invalide"})()})}),de.canManageDebts&&r.jsx(ge,{align:"center",children:r.jsxs(a,{display:"flex",gap:1,children:[r.jsx(F,{title:"Voir détails",children:r.jsx(U,{size:"small",onClick:()=>Ve(e),children:r.jsx(se,{fontSize:"small"})})}),"paid"!==e.statut&&r.jsx(F,{title:"Ajouter paiement",children:r.jsx(U,{size:"small",color:"primary",onClick:()=>(e=>{k(e),R(e.montantRestantCDF),L("cash"),q(""),oe("CDF"),w(!0),$(""),X("")})(e),children:r.jsx(G,{fontSize:"small"})})})]})})]},e.id)}catch(t){return console.error("Error rendering debt row:",e,t),r.jsx(xe,{children:r.jsx(ge,{colSpan:de.canManageDebts?9:8,children:r.jsxs(o,{color:"error",variant:"body2",children:["Erreur d'affichage pour cette dette. ID: ",e.id||"Inconnu"]})})},e.id||`error-${Math.random()}`)}})})]}),r.jsx(Re,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===C?n.length:C,page:-1===C?0:j,onPageChange:(e,t)=>{-1!==C&&D(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);v(t),D(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===C?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),r.jsxs(Me,{open:b,onClose:Oe,maxWidth:"sm",fullWidth:!0,children:[r.jsx(Ie,{children:"Ajouter un Paiement"}),r.jsxs(Ne,{children:[B&&r.jsx(O,{severity:"error",sx:{mb:2},children:B}),W&&r.jsx(O,{severity:"success",sx:{mb:2},children:W}),T&&r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsxs(ce,{item:!0,xs:12,children:[r.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Client: ",ve(T)]}),r.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Ba(T.montantRestantCDF,"CDF")]})]}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(Ya,{label:"Montant du paiement",value:A,onChange:e=>R(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Méthode de paiement"}),r.jsxs(Ce,{value:N,label:"Méthode de paiement",onChange:e=>L(e.target.value),children:[r.jsx(J,{value:"cash",children:"Cash"}),r.jsx(J,{value:"banque",children:"Banque"}),r.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:V,onChange:e=>q(e.target.value)})})]})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:Oe,children:"Annuler"}),r.jsx(I,{onClick:Be,variant:"contained",disabled:!T||A<=0,children:"Enregistrer le Paiement"})]})]}),r.jsxs(Me,{open:E,onClose:qe,maxWidth:"md",fullWidth:!0,children:[r.jsx(Ie,{children:"Détails de la Dette"}),r.jsx(Ne,{children:T&&r.jsxs(ce,{container:!0,spacing:2,children:[r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Achetés à Crédit:"}),r.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:Se(T)})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"ID Dette:"}),r.jsx(o,{variant:"body1",children:T.id})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),r.jsx(o,{variant:"body2",color:"text.secondary",children:T.venteId})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Client:"}),r.jsx(o,{variant:"body1",sx:{fontWeight:"medium"},children:ve(T)}),T.telephoneClient&&r.jsxs(a,{sx:{mt:.5},children:[r.jsx(o,{variant:"caption",color:"text.secondary",children:"Téléphone:"}),r.jsx(o,{variant:"body2",color:"primary",children:T.telephoneClient})]}),T.adresseClient&&r.jsxs(a,{sx:{mt:.5},children:[r.jsx(o,{variant:"caption",color:"text.secondary",children:"Adresse:"}),r.jsx(o,{variant:"body2",color:"text.primary",children:T.adresseClient})]})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Statut:"}),r.jsx(M,{icon:Te(T.statut),label:Pe(T.statut),color:Ee(T.statut)})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Statut Paiement:"}),r.jsx(a,{display:"flex",alignItems:"center",gap:1,sx:{mt:.5},children:de.canManageDebts?r.jsx(Xe,{control:r.jsx(Ke,{checked:"paye"===T.statutPaiement,onChange:n=>(async(n,r)=>{if(!de.canManageDebts)return;if(!window.confirm(`Êtes-vous sûr de vouloir changer le statut de paiement à "${ke(r)}" ?\n\nCette action modifiera le statut de paiement de la dette.`))return;const a=e.map(e=>e.id===n?{...e,statutPaiement:r}:e);t(a),await ma.setDebts(a),T&&T.id===n&&k({...T,statutPaiement:r}),X(`Statut de paiement mis à jour: ${ke(r)}`),setTimeout(()=>{X("")},3e3)})(T.id,n.target.checked?"paye":"impaye"),color:"success",size:"small"}),label:r.jsxs(a,{display:"flex",alignItems:"center",gap:.5,children:[Ae(T.statutPaiement),r.jsx(o,{variant:"body2",children:ke(T.statutPaiement)})]}),labelPlacement:"end"}):r.jsx(M,{icon:Ae(T.statutPaiement),label:ke(T.statutPaiement),color:(e=>{switch(e){case"paye":return"success";case"impaye":return"error";default:return"default"}})(T.statutPaiement)})})]}),r.jsxs(ce,{item:!0,xs:12,md:4,children:[r.jsx(o,{variant:"subtitle2",children:"Montant Dû:"}),r.jsx(o,{variant:"body1",color:"primary",sx:{fontWeight:"bold"},children:Ba(T.montantTotalCDF,"CDF")})]}),r.jsxs(ce,{item:!0,xs:12,md:4,children:[r.jsx(o,{variant:"subtitle2",children:"Montant Payé:"}),r.jsx(o,{variant:"body1",color:"success.main",children:Ba(T.montantPayeCDF,"CDF")})]}),r.jsxs(ce,{item:!0,xs:12,md:4,children:[r.jsx(o,{variant:"subtitle2",children:"Montant Restant:"}),r.jsx(o,{variant:"body1",color:"error",children:Ba(T.montantRestantCDF,"CDF")})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Progression du Paiement:"}),r.jsxs(a,{sx:{width:"100%",mb:1},children:[r.jsx(le,{variant:"determinate",value:Qe(T),color:100===Qe(T)?"success":"overdue"===T.statut?"error":"primary",sx:{height:10,borderRadius:5}}),r.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",mt:1},children:[r.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(Qe(T)),"% payé"]}),r.jsx(o,{variant:"caption",color:"text.secondary",children:T.montantRestantCDF>0?`${Ba(T.montantRestantCDF,"CDF")} restant`:"Entièrement payé"})]})]})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Date de Création:"}),r.jsx(o,{variant:"body1",children:(()=>{const e=be(T.dateCreation);return e?Ut(e,"dd/MM/yyyy",{locale:Da}):"Date invalide"})()})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsx(o,{variant:"subtitle2",children:"Date d'Échéance:"}),r.jsx(o,{variant:"body1",color:"overdue"===T.statut?"error":"text.primary",children:(()=>{const e=be(T.dateEcheance);return e?Ut(e,"dd/MM/yyyy",{locale:Da}):"Date invalide"})()})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Historique des Paiements (",T.paiements.length,")"]}),0===T.paiements.length?r.jsx(O,{severity:"info",children:"Aucun paiement enregistré"}):r.jsx(p,{dense:!0,children:T.paiements.map((e,t)=>r.jsxs(ft.Fragment,{children:[r.jsx(x,{children:r.jsx(y,{primary:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[_e(e.methodePaiement),r.jsx(o,{variant:"body2",children:"USD"===e.deviseOriginale?`${Ba(e.montantUSD||0,"USD")} (≈ ${Ba(e.montantCDF,"CDF")})`:`${Ba(e.montantCDF,"CDF")} (≈ ${Ba(e.montantUSD||0,"USD")})`}),r.jsx(M,{label:ze(e.methodePaiement),size:"small"})]}),secondary:r.jsxs(a,{children:[r.jsx(o,{variant:"caption",children:(()=>{const t=be(e.datePaiement);return t?Ut(t,"dd/MM/yyyy HH:mm",{locale:Da}):"Date invalide"})()}),e.notes&&r.jsx(o,{variant:"caption",display:"block",children:e.notes})]})})}),t<T.paiements.length-1&&r.jsx(h,{})]},e.id))})]}),de.canManageDebts&&"paid"!==T.statut&&Z&&r.jsx(ce,{item:!0,xs:12,children:r.jsxs(c,{elevation:2,sx:{p:2,mt:2,bgcolor:"background.default"},children:[r.jsxs(o,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[r.jsx(G,{}),"Ajouter un Paiement"]}),r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsx(ce,{item:!0,xs:12,children:r.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Ba(T.montantRestantCDF,"CDF")]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ya,{label:"Montant à payer",value:A,onChange:e=>R(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Méthode de paiement"}),r.jsxs(Ce,{value:N,onChange:e=>L(e.target.value),label:"Méthode de paiement",children:[r.jsx(J,{value:"cash",children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(f,{fontSize:"small"}),"Cash"]})}),r.jsx(J,{value:"mobile_money",children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(He,{fontSize:"small"}),"Mobile Money"]})}),r.jsx(J,{value:"banque",children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx($e,{fontSize:"small"}),"Banque"]})})]})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Notes (optionnel)",value:V,onChange:e=>q(e.target.value),multiline:!0,rows:2,placeholder:"Ajouter des notes sur ce paiement..."})}),B&&r.jsx(ce,{item:!0,xs:12,children:r.jsx(O,{severity:"error",children:B})}),W&&r.jsx(ce,{item:!0,xs:12,children:r.jsx(O,{severity:"success",children:W})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(a,{display:"flex",gap:1,justifyContent:"flex-end",children:[r.jsx(I,{variant:"outlined",onClick:()=>{ae(!1),R(0),q(""),oe("CDF"),$(""),X("")},children:"Annuler"}),r.jsx(I,{variant:"contained",color:"success",onClick:()=>{R(T.montantRestantCDF),q("Paiement complet")},startIcon:r.jsx(G,{}),children:"Paiement Complet"}),r.jsx(I,{variant:"contained",color:"primary",onClick:Be,startIcon:r.jsx(G,{}),disabled:A<=0,children:"Payer"})]})})]})]})}),T.notes&&r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"subtitle2",children:"Notes:"}),r.jsx(o,{variant:"body1",children:T.notes})]})]})}),r.jsxs(Le,{children:[de.canManageDebts&&T&&"paid"!==T.statut&&!Z&&r.jsx(I,{variant:"contained",color:"primary",onClick:()=>{ae(!0),R(0),L("cash"),q(""),$(""),X("")},startIcon:r.jsx(G,{}),sx:{mr:1},children:"Ajouter Paiement"}),r.jsx(I,{onClick:qe,children:"Fermer"})]})]})]})},si=(e,t="dd/MM/yyyy")=>{try{const n="string"==typeof e?new Date(e):e;return Lt(n)?Ut(n,t,{locale:Da}):"Date invalide"}catch(fi){return console.warn("Invalid date value:",e,fi),"Date invalide"}},oi=()=>{var e;try{const[t,n]=vt.useState([]),[i,s]=vt.useState([]),[l,d]=vt.useState(""),[u,m]=vt.useState(""),[h,p]=vt.useState(""),[x,g]=vt.useState(0),[y,j]=vt.useState(10),[D,C]=vt.useState(!1),[S,v]=vt.useState(null),[b,w]=vt.useState({description:"",montantCDF:0,categorie:"",dateDepense:si(new Date,"yyyy-MM-dd"),notes:""}),[E,P]=vt.useState(""),[T,k]=vt.useState(""),[A,R]=vt.useState({tauxChangeUSDCDF:2800}),[N,L]=vt.useState(!1),[V,B]=vt.useState(!1),[_,$]=vt.useState(null),[z,W]=vt.useState(!1),X=ha.getUserPermissions(),Q=ha.getCurrentUser(),H=["Loyer","Électricité","Eau","Internet","Téléphone","Transport","Carburant","Fournitures de bureau","Marketing","Maintenance","Assurance","Taxes","Salaires","Formation","Équipement","Autres"];vt.useEffect(()=>{G()},[]),vt.useEffect(()=>{Y()},[t,l,u,h]),vt.useEffect(()=>{-1===y&&j(i.length||1)},[i.length,y]);const G=async()=>{const e=await ma.getExpenses(),t=await ma.getSettings();n(e),R(t)},Y=()=>{let e=t;if(l&&(e=e.filter(e=>e.description.toLowerCase().includes(l.toLowerCase())||e.categorie.toLowerCase().includes(l.toLowerCase())||e.notes&&e.notes.toLowerCase().includes(l.toLowerCase()))),u&&(e=e.filter(e=>e.categorie===u)),h){const t=new Date;let n,r;switch(h){case"today":n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),r=new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59);break;case"this_month":n=It(t),r=Nt(t);break;case"last_month":const e=new Date(t.getFullYear(),t.getMonth()-1,1);n=It(e),r=Nt(e);break;default:n=new Date(0),r=new Date}e=e.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,{start:n,end:r})})}s(e)},K=e=>{e?(v(e),w({description:e.description,montantCDF:e.montantCDF,categorie:e.categorie,dateDepense:si(e.dateDepense,"yyyy-MM-dd"),notes:e.notes||""})):(v(null),w({description:"",montantCDF:0,categorie:"",dateDepense:si(new Date,"yyyy-MM-dd"),notes:""})),C(!0),P(""),k("")},Z=()=>{C(!1),v(null),P(""),k(""),L(!1)},ae=async()=>{var e;if(b.description.trim())if(b.montantCDF<=0)P("Le montant doit être supérieur à 0");else if(b.categorie)if(b.dateDepense){if(S){const e={...S,description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/A.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0},r=t.map(t=>t.id===S.id?e:t);n(r),await ma.setExpenses(r),k("Dépense mise à jour avec succès")}else{const a=N||(null==(e=A.impression)?void 0:e.impressionAutomatique)||!1,i=a?await ei.generateExpenseReceiptNumber():void 0,s={id:Date.now().toString(),description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/A.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0,creePar:(null==Q?void 0:Q.nom)||"Inconnu",numeroRecu:i},o=[...t,s];if(n(o),await ma.setExpenses(o),k("Dépense créée avec succès"),a)try{W(!0);const e=await ei.createExpenseReceiptData(s);$(e),B(!0)}catch(r){console.error("Erreur lors de la génération du reçu:",r),P("Erreur lors de la génération du reçu")}finally{W(!1)}}setTimeout(()=>{Z()},1500)}else P("La date est requise");else P("La catégorie est requise");else P("La description est requise")},ie=async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer cette dépense "${e.description}" ?`)){const r=t.filter(t=>t.id!==e.id);n(r),await ma.setExpenses(r),k("Dépense supprimée avec succès"),setTimeout(()=>k(""),3e3)}},se=(e,t)=>{-1!==y&&g(t)},oe=e=>{const t=parseInt(e.target.value,10);j(t),g(0)},le=new Date,de={start:It(le),end:Nt(le)},ue=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&t.toDateString()===le.toDateString()}),Se=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,de)}),ve=t.length,be=ue.reduce((e,t)=>e+t.montantCDF,0),fe=Se.reduce((e,t)=>e+t.montantCDF,0),we=t.reduce((e,t)=>e+t.montantCDF,0),Ee=t.reduce((e,t)=>(e[t.categorie]=(e[t.categorie]||0)+t.montantUSD,e),{}),Te=Object.entries(Ee).sort(([,e],[,t])=>t-e).slice(0,5),ke=()=>{const e=`SmartBoutique_Depenses_${si(new Date,"yyyy-MM-dd")}.csv`;Or.downloadCSV(i,$r,e),k("Dépenses exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>k(""),3e3)};return r.jsxs(a,{children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[r.jsx(o,{variant:"h4",children:"Gestion des Dépenses"}),r.jsxs(a,{display:"flex",gap:2,children:[r.jsx(I,{variant:"outlined",startIcon:r.jsx(Ue,{}),onClick:ke,children:"Exporter les Dépenses"}),X.canManageExpenses&&r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>K(),children:"Nouvelle Dépense"})]})]}),T&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>k(""),children:T}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du jour"}),r.jsx(o,{variant:"h6",children:ue.length}),r.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_a(be,A.tauxChangeUSDCDF).primaryAmount," ",_a(be,A.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(be,A.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(f,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du mois"}),r.jsx(o,{variant:"h6",children:Se.length}),r.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_a(fe,A.tauxChangeUSDCDF).primaryAmount," ",_a(fe,A.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(fe,A.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(tt,{color:"error",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),r.jsx(o,{variant:"h6",children:ve}),r.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_a(we,A.tauxChangeUSDCDF).primaryAmount," ",_a(we,A.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_a(we,A.tauxChangeUSDCDF).secondaryAmount]})]}),r.jsx(nt,{color:"info",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Catégories"}),r.jsx(o,{variant:"h6",children:Object.keys(Ee).length}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["Top: ",(null==(e=Te[0])?void 0:e[0])||"N/A"]})]}),r.jsx(rt,{color:"warning",sx:{fontSize:40}})]})})})})]}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par description, catégorie ou notes...",value:l,onChange:e=>d(e.target.value),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Fe,{})})}})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Catégorie"}),r.jsxs(Ce,{value:u,label:"Catégorie",onChange:e=>m(e.target.value),children:[r.jsx(J,{value:"",children:"Toutes les catégories"}),H.map(e=>r.jsx(J,{value:e,children:e},e))]})]})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Période"}),r.jsxs(Ce,{value:h,label:"Période",onChange:e=>p(e.target.value),children:[r.jsx(J,{value:"",children:"Toutes les périodes"}),r.jsx(J,{value:"today",children:"Aujourd'hui"}),r.jsx(J,{value:"this_month",children:"Ce mois"}),r.jsx(J,{value:"last_month",children:"Mois dernier"})]})]})})]})}),Te.length>0&&r.jsxs(c,{sx:{p:2,mb:3},children:[r.jsx(o,{variant:"h6",gutterBottom:!0,children:"Top 5 Catégories"}),r.jsx(ce,{container:!0,spacing:1,children:Te.map(([e,t])=>r.jsx(ce,{item:!0,children:r.jsx(M,{label:`${e}: ${Ba(t,"CDF")}`,color:"primary",variant:"outlined"})},e))})]}),r.jsxs(me,{component:c,children:[r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Description"}),r.jsx(ge,{children:"Catégorie"}),r.jsx(ge,{align:"right",children:"Montant CDF"}),r.jsx(ge,{align:"right",children:"Montant USD"}),r.jsx(ge,{children:"Date"}),r.jsx(ge,{children:"Créé par"}),X.canManageExpenses&&r.jsx(ge,{align:"center",children:"Actions"})]})}),r.jsx(ye,{children:(-1===y?i:i.slice(x*y,x*y+y)).map(e=>r.jsxs(xe,{hover:!0,onClick:()=>K(e),sx:{cursor:"pointer"},children:[r.jsx(ge,{children:r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",children:e.description}),e.notes&&r.jsx(o,{variant:"caption",color:"text.secondary",children:e.notes})]})}),r.jsx(ge,{children:r.jsx(M,{label:e.categorie,size:"small"})}),r.jsx(ge,{align:"right",children:Ba(e.montantCDF,"CDF")}),r.jsx(ge,{align:"right",children:e.montantUSD?Ba(e.montantUSD,"USD"):"-"}),r.jsx(ge,{children:si(e.dateDepense)}),r.jsx(ge,{children:e.creePar}),X.canManageExpenses&&r.jsx(ge,{align:"center",children:r.jsxs(a,{display:"flex",gap:1,children:[r.jsx(F,{title:"Modifier",children:r.jsx(U,{size:"small",onClick:()=>K(e),children:r.jsx(Ae,{fontSize:"small"})})}),ha.hasRole(["super_admin","admin"])&&r.jsx(F,{title:"Supprimer",children:r.jsx(U,{size:"small",color:"error",onClick:()=>ie(e),children:r.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]}),r.jsx(Re,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:i.length,rowsPerPage:-1===y?i.length:y,page:-1===y?0:x,onPageChange:se,onRowsPerPageChange:oe,labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),r.jsxs(Me,{open:D,onClose:Z,maxWidth:"md",fullWidth:!0,children:[r.jsx(Ie,{children:S?"Modifier la Dépense":"Nouvelle Dépense"}),r.jsxs(Ne,{children:[E&&r.jsx(O,{severity:"error",sx:{mb:2},children:E}),T&&r.jsx(O,{severity:"success",sx:{mb:2},children:T}),r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Description *",value:b.description,onChange:e=>w({...b,description:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(Ya,{label:"Montant de la dépense",value:b.montantCDF,onChange:e=>w({...b,montantCDF:e}),min:0,max:5e6,step:50,exchangeRate:A.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Catégorie *"}),r.jsx(Ce,{value:b.categorie,label:"Catégorie *",onChange:e=>w({...b,categorie:e.target.value}),children:H.map(e=>r.jsx(J,{value:e,children:e},e))})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Date de la dépense *",type:"date",value:b.dateDepense,onChange:e=>w({...b,dateDepense:e.target.value}),InputLabelProps:{shrink:!0}})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:b.notes,onChange:e=>w({...b,notes:e.target.value})})})]})]}),r.jsxs(Le,{children:[!S&&r.jsx(Xe,{control:r.jsx(Je,{checked:N,onChange:e=>L(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),z&&r.jsxs(a,{display:"flex",alignItems:"center",gap:1,mr:2,children:[r.jsx(qe,{size:20}),r.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),r.jsx(I,{onClick:Z,children:"Annuler"}),r.jsx(I,{onClick:ae,variant:"contained",disabled:z,children:S?"Mettre à jour":"Créer"})]})]}),r.jsx(ri,{open:V,onClose:()=>B(!1),receiptData:_,onPrintSuccess:()=>{k("Reçu imprimé avec succès"),setTimeout(()=>k(""),3e3)}})]})}catch(fi){return console.error("ExpensesPage error:",fi),r.jsxs(a,{p:3,children:[r.jsx(o,{variant:"h4",gutterBottom:!0,children:"Dépenses"}),r.jsx(O,{severity:"error",children:"Une erreur s'est produite lors du chargement de la page des dépenses. Veuillez recharger l'application ou contacter le support technique."})]})}};function li(e){const{children:t,value:n,index:i,...s}=e;return r.jsx("div",{role:"tabpanel",hidden:n!==i,id:`reports-tabpanel-${i}`,"aria-labelledby":`reports-tab-${i}`,...s,children:n===i&&r.jsx(a,{sx:{p:3},children:t})})}const ci=()=>{const[e,t]=vt.useState(0),[n,i]=vt.useState("this_month"),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,p]=vt.useState([]),[x,g]=vt.useState([]),[y,j]=vt.useState([]),[v,b]=vt.useState([]),[E,P]=vt.useState([]),[F,U]=vt.useState([]),[T,k]=vt.useState({tauxChangeUSDCDF:2800}),[A,R]=vt.useState(""),[N,L]=vt.useState("");vt.useEffect(()=>{(async()=>{try{const e=await ma.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}})()},[]),vt.useEffect(()=>{V()},[]),vt.useEffect(()=>{q()},[m,v,n,s,d]);const V=async()=>{try{console.log("ReportsPage: Loading data..."),p(await ma.getSales()),g(await ma.getProducts()),j(await ma.getDebts()),b(await ma.getExpenses()),console.log("ReportsPage: Data loaded successfully")}catch(e){console.error("ReportsPage: Error loading data:",e),p([]),g([]),j([]),b([])}},q=()=>{const e=new Date;let t,r;switch(n){case"today":t=Tt(e),r=kt(e);break;case"this_week":t=At(e,7),r=e;break;case"this_month":default:t=It(e),r=Nt(e);break;case"last_month":const n=new Date(e.getFullYear(),e.getMonth()-1,1);t=It(n),r=Nt(n);break;case"custom":s&&d?(t=new Date(s),r=new Date(d)):(t=It(e),r=Nt(e))}const a=m.filter(e=>{const n=new Date(e.datevente);return Ot(n,{start:t,end:r})}),i=v.filter(e=>{const n=new Date(e.dateDepense);return Ot(n,{start:t,end:r})});P(a),U(i)},B=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,_=e=>{try{const t=null==e||isNaN(e)?0:e,n=t/((null==T?void 0:T.tauxChangeUSDCDF)||2800);return{primary:`${t.toLocaleString("fr-FR")} CDF`,secondary:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`}}catch(t){return console.error("ReportsPage: Error formatting dual currency:",t),{primary:"0 CDF",secondary:"$0.00"}}},$=E.reduce((e,t)=>e+t.totalCDF,0),z=E.length,W=z>0?$/z:0,X=F.reduce((e,t)=>e+t.montantCDF,0),Q=$-X,H=E.reduce((e,t)=>(t.produits.forEach(t=>{e[t.produitId]||(e[t.produitId]={nom:t.nomProduit,quantite:0,revenue:0}),e[t.produitId].quantite+=t.quantite,e[t.produitId].revenue+=t.totalCDF}),e),{}),G=Object.values(H).sort((e,t)=>t.revenue-e.revenue).slice(0,10),Y=E.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+t.totalCDF,e),{}),K=E.reduce((e,t)=>(t.produits.forEach(t=>{const n=x.find(e=>e.id===t.produitId);n&&(e[n.categorie]=(e[n.categorie]||0)+t.totalCDF)}),e),{}),Z=E.reduce((e,t)=>{const n=Ut(new Date(t.datevente),"yyyy-MM-dd");return e[n]=(e[n]||0)+t.totalCDF,e},{}),re=Array.from({length:30},(e,t)=>{const n=At(new Date,29-t),r=Ut(n,"yyyy-MM-dd");return{date:Ut(n,"dd/MM"),revenue:Z[r]||0}}),ae={labels:re.map(e=>e.date),datasets:[{label:"Profit (USD)",data:re.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]},ie={labels:["Cash","Banque","Mobile Money"],datasets:[{data:[Y.cash||0,Y.banque||0,Y.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]},se={labels:Object.keys(K),datasets:[{label:"Profit par catégorie (USD)",data:Object.values(K),backgroundColor:["#FF6384","#36A2EB","#FFCE56","#4BC0C0","#9966FF","#FF9F40"]}]};return r.jsxs(a,{children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[r.jsx(o,{variant:"h4",children:"Rapports et Analyses"}),r.jsx(I,{variant:"contained",startIcon:r.jsx(Ue,{}),onClick:()=>{try{const e=(null==T?void 0:T.tauxChangeUSDCDF)||2800,t=(e,t)=>{const n=null==e||isNaN(e)?0:e;return"USD"===t?`$${Aa(n,2,2)}`:`${Aa(n,0,0)} CDF`},r=[{label:"Période",value:"custom"===n?`${s} - ${d}`:n},{label:"Date de Génération",value:Ut(new Date,"dd/MM/yyyy HH:mm")},{label:"Total Ventes",value:z.toString()},{label:"Chiffre d'Affaires (CDF)",value:t($,"CDF")},{label:"Chiffre d'Affaires (USD)",value:t($/e,"USD")},{label:"Total Dépenses (CDF)",value:t(X,"CDF")},{label:"Total Dépenses (USD)",value:t(X/e,"USD")},{label:"Profit Final (CDF)",value:t(Q,"CDF")},{label:"Profit Final (USD)",value:t(Q/e,"USD")},{label:"Vente Moyenne (CDF)",value:t(W,"CDF")},{label:"Vente Moyenne (USD)",value:t(W/e,"USD")}],a=G.map((n,r)=>({rang:r+1,produit:n.nom||"N/A",quantiteVendue:n.quantite||0,revenuesCDF:t(n.revenue||0,"CDF"),revenuesUSD:t((n.revenue||0)/e,"USD"),revenuMoyenCDF:t((n.revenue||0)/(n.quantite||1),"CDF"),revenuMoyenUSD:t((n.revenue||0)/(n.quantite||1)/e,"USD")})),i=new Date;let o,l;switch(n){case"today":o=Tt(i),l=kt(i);break;case"this_week":o=At(i,7),l=i;break;case"this_month":default:o=It(i),l=Nt(i);break;case"last_month":const e=new Date(i.getFullYear(),i.getMonth()-1,1);o=It(e),l=Nt(e);break;case"custom":s&&d?(o=new Date(s),l=new Date(d)):(o=It(i),l=Nt(i))}const c=Vt({start:o,end:l}).map(n=>{const r=Ut(n,"yyyy-MM-dd"),a=E.filter(e=>Ut(new Date(e.datevente),"yyyy-MM-dd")===r).reduce((e,t)=>e+t.totalCDF,0);return{date:Ut(n,"dd/MM/yyyy"),revenueCDF:t(a,"CDF"),revenueUSD:t(a/e,"USD")}}),u=qt({start:o,end:l},{weekStartsOn:1}).map(n=>{const r=Bt(n,{weekStartsOn:1}),a=E.filter(e=>{const t=new Date(e.datevente);return Ot(t,{start:n,end:r})}).reduce((e,t)=>e+t.totalCDF,0);return{semaine:`${Ut(n,"dd/MM")} - ${Ut(r,"dd/MM/yyyy")}`,revenueCDF:t(a,"CDF"),revenueUSD:t(a/e,"USD")}}),m=_t({start:o,end:l}).map(n=>{const r=Nt(n),a=E.filter(e=>{const t=new Date(e.datevente);return Ot(t,{start:n,end:r})}).reduce((e,t)=>e+t.totalCDF,0);return{mois:Ut(n,"MMMM yyyy"),revenueCDF:t(a,"CDF"),revenueUSD:t(a/e,"USD")}}),h=y.filter(e=>"paid"!==e.statut),p=h.reduce((e,t)=>e+t.montantRestantCDF,0),g={nombreDettes:h.length,montantTotalCDF:t(p,"CDF"),montantTotalUSD:t(p/e,"USD")},j=Object.entries(K).map(([n,r])=>{const a=E.reduce((e,t)=>e+t.produits.filter(e=>{const t=x.find(t=>t.id===e.produitId);return t&&t.categorie===n}).length,0);return{categorie:n||"Non catégorisé",revenuesCDF:t(r,"CDF"),revenuesUSD:t(r/e,"USD"),nombreVentes:a}});let D="SmartBoutique - Rapport d'Analyse\n\n";D+="RÉSUMÉ DU RAPPORT\n",D+="Métrique,Valeur\n",r.forEach(e=>{D+=`"${e.label}","${e.value}"\n`}),D+="\n\nTOP PRODUITS LES PLUS VENDUS\n",D+="Rang,Produit,Quantité Vendue,Revenus (CDF),Revenus (USD),Revenu Moyen (CDF),Revenu Moyen (USD)\n",a.forEach(e=>{D+=`${e.rang},"${e.produit}",${e.quantiteVendue},"${e.revenuesCDF}","${e.revenuesUSD}","${e.revenuMoyenCDF}","${e.revenuMoyenUSD}"\n`}),D+="\n\nREVENU JOURNALIER\n",D+="Date,Revenus (CDF),Revenus (USD)\n",c.forEach(e=>{D+=`"${e.date}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nREVENU PAR SEMAINE\n",D+="Semaine,Revenus (CDF),Revenus (USD)\n",u.forEach(e=>{D+=`"${e.semaine}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nREVENU PAR MOIS\n",D+="Mois,Revenus (CDF),Revenus (USD)\n",m.forEach(e=>{D+=`"${e.mois}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nDETTES DUES\n",D+="Métrique,Valeur\n",D+=`"Nombre de Dettes Impayées","${g.nombreDettes}"\n`,D+=`"Montant Total (CDF)","${g.montantTotalCDF}"\n`,D+=`"Montant Total (USD)","${g.montantTotalUSD}"\n`,D+="\n\nPERFORMANCE PAR CATÉGORIE\n",D+="Catégorie,Revenus (CDF),Revenus (USD),Nombre de Ventes\n",j.forEach(e=>{D+=`"${e.categorie}","${e.revenuesCDF}","${e.revenuesUSD}",${e.nombreVentes}\n`});const C=`rapport_${Ut(new Date,"yyyy-MM-dd_HH-mm")}.csv`,S=new Blob(["\ufeff"+D],{type:"text/csv;charset=utf-8"}),v=URL.createObjectURL(S),b=document.createElement("a");b.href=v,b.download=C,document.body.appendChild(b),b.click(),document.body.removeChild(b),URL.revokeObjectURL(v),R("Rapport exporté avec succès (compatible Excel)"),setTimeout(()=>R(""),3e3)}catch(e){console.error("Erreur lors de l'exportation du rapport:",e),L("Erreur lors de l'exportation du rapport. Veuillez réessayer."),setTimeout(()=>L(""),5e3)}},children:"Exporter le Rapport"})]}),A&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:A}),N&&r.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>L(""),children:N}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Période"}),r.jsxs(Ce,{value:n,label:"Période",onChange:e=>i(e.target.value),children:[r.jsx(J,{value:"today",children:"Aujourd'hui"}),r.jsx(J,{value:"this_week",children:"Cette semaine"}),r.jsx(J,{value:"this_month",children:"Ce mois"}),r.jsx(J,{value:"last_month",children:"Mois dernier"}),r.jsx(J,{value:"custom",children:"Personnalisée"})]})]})}),"custom"===n&&r.jsxs(r.Fragment,{children:[r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsx(ne,{fullWidth:!0,label:"Date de début",type:"date",value:s,onChange:e=>l(e.target.value),InputLabelProps:{shrink:!0}})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsx(ne,{fullWidth:!0,label:"Date de fin",type:"date",value:d,onChange:e=>u(e.target.value),InputLabelProps:{shrink:!0}})})]})]})}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Chiffre d'Affaires"}),r.jsx(o,{variant:"h6",color:"primary",fontWeight:"medium",children:_($*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_($*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),r.jsx(Se,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Nombre de Ventes"}),r.jsx(o,{variant:"h6",color:"success.main",children:z})]}),r.jsx(C,{color:"success",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),r.jsx(o,{variant:"h6",color:"error",fontWeight:"medium",children:_(X*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(X*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),r.jsx(f,{color:"error",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Après Dépenses"}),r.jsx(o,{variant:"h6",color:Q>=0?"success.main":"error",fontWeight:"medium",children:_(Q*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(Q*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),r.jsx(S,{color:Q>=0?"success":"error",sx:{fontSize:40}})]})})})})]}),r.jsx(c,{sx:{mb:3},children:r.jsxs(at,{value:e,onChange:(e,n)=>{t(n)},"aria-label":"reports tabs",children:[r.jsx(it,{label:"Tendances",icon:r.jsx(Se,{})}),r.jsx(it,{label:"Produits",icon:r.jsx(D,{})}),r.jsx(it,{label:"Analyses",icon:r.jsx(w,{})})]})}),r.jsx(li,{value:e,index:0,children:r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,md:8,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Évolution des Ventes (30 derniers jours)"}),r.jsx(te,{children:r.jsx($t,{data:ae,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})}),r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Méthodes de Paiement"}),r.jsx(te,{children:r.jsx(zt,{data:ie,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Performance par Catégorie"}),r.jsx(te,{children:r.jsx(Wt,{data:se,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})})]})}),r.jsx(li,{value:e,index:1,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Top 10 Produits les Plus Vendus"}),r.jsx(te,{children:r.jsx(me,{children:r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Rang"}),r.jsx(ge,{children:"Produit"}),r.jsx(ge,{align:"right",children:"Quantité Vendue"}),r.jsx(ge,{align:"right",children:"Profit"}),r.jsx(ge,{align:"right",children:"Profit Moyen"})]})}),r.jsx(ye,{children:G.map((e,t)=>r.jsxs(xe,{children:[r.jsx(ge,{children:r.jsx(M,{label:`#${t+1}`,color:t<3?"primary":"default",size:"small"})}),r.jsx(ge,{children:e.nom}),r.jsx(ge,{align:"right",children:e.quantite}),r.jsx(ge,{align:"right",children:B(e.revenue,"USD")}),r.jsx(ge,{align:"right",children:B(e.revenue/e.quantite,"USD")})]},t))})]})})})]})}),r.jsx(li,{value:e,index:2,children:r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Métriques de Performance"}),r.jsx(te,{children:r.jsxs(ce,{container:!0,spacing:2,children:[r.jsxs(ce,{item:!0,xs:6,children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"Vente Moyenne"}),r.jsx(o,{variant:"h6",children:B(W,"USD")})]}),r.jsxs(ce,{item:!0,xs:6,children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"Produits Vendus"}),r.jsx(o,{variant:"h6",children:E.reduce((e,t)=>e+t.produits.reduce((e,t)=>e+t.quantite,0),0)})]}),r.jsxs(ce,{item:!0,xs:6,children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"Panier Moyen"}),r.jsxs(o,{variant:"h6",children:[z>0?(E.reduce((e,t)=>e+t.produits.length,0)/z).toFixed(1):"0"," articles"]})]})]})})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Répartition des Profits"}),r.jsx(te,{children:r.jsxs(ce,{container:!0,spacing:2,children:[r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes Cash"}),r.jsx(o,{variant:"h6",color:"success.main",children:B(E.filter(e=>"cash"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes à Crédit"}),r.jsx(o,{variant:"h6",color:"warning.main",children:B(E.filter(e=>"credit"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),r.jsx(h,{sx:{width:"100%",my:1}}),r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(o,{variant:"body2",color:"text.secondary",children:"Dettes Impayées"}),r.jsx(o,{variant:"h6",color:"error",children:B(y.filter(e=>"paid"!==e.statut).reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]})]})})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Analyse des Dépenses par Catégorie"}),r.jsx(te,{children:r.jsx(me,{children:r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Catégorie"}),r.jsx(ge,{align:"right",children:"Nombre"}),r.jsx(ge,{align:"right",children:"Montant Total"}),r.jsx(ge,{align:"right",children:"Montant Moyen"}),r.jsx(ge,{align:"right",children:"% du Total"})]})}),r.jsx(ye,{children:Object.entries(F.reduce((e,t)=>(e[t.categorie]||(e[t.categorie]={count:0,total:0}),e[t.categorie].count+=1,e[t.categorie].total+=t.montantCDF,e),{})).sort(([,e],[,t])=>t.total-e.total).map(([e,t])=>r.jsxs(xe,{children:[r.jsx(ge,{children:e}),r.jsx(ge,{align:"right",children:t.count}),r.jsx(ge,{align:"right",children:B(t.total,"USD")}),r.jsx(ge,{align:"right",children:B(t.total/t.count,"USD")}),r.jsx(ge,{align:"right",children:X>0?`${(t.total/X*100).toFixed(1)}%`:"0%"})]},e))})]})})})]})})]})})]})},di=()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState([]),[s,d]=vt.useState(""),[u,m]=vt.useState(""),[h,p]=vt.useState(""),[x,g]=vt.useState(0),[y,j]=vt.useState(10),[D,C]=vt.useState(!1),[S,v]=vt.useState(null),[b,f]=vt.useState(!1),[w,P]=vt.useState({nom:"",email:"",role:"employee",motDePasse:"",actif:!0}),[T,k]=vt.useState(""),[A,R]=vt.useState(""),N=ha.getUserPermissions(),L=ha.getCurrentUser();vt.useEffect(()=>{V()},[]),vt.useEffect(()=>{B()},[e,s,u,h]),vt.useEffect(()=>{-1===y&&j(n.length||1)},[n.length,y]);const V=async()=>{const e=await ma.getUsers();t(e)},B=()=>{let t=e;if(s&&(t=t.filter(e=>e.nom.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))),u&&(t=t.filter(e=>e.role===u)),h){const e="active"===h;t=t.filter(t=>t.actif===e)}i(t)},_=e=>{switch(e){case"super_admin":return"Super Admin";case"admin":return"Administrateur";case"employee":return"Employé";default:return e}},$=e=>{switch(e){case"super_admin":return"error";case"admin":return"warning";case"employee":return"primary";default:return"default"}},z=e=>{switch(e){case"super_admin":case"admin":return r.jsx(ot,{});default:return r.jsx(lt,{})}},W=e=>{e?(v(e),P({nom:e.nom,email:e.email,role:e.role,motDePasse:"",actif:e.actif})):(v(null),P({nom:"",email:"",role:"employee",motDePasse:"",actif:!0})),C(!0),f(!1),k(""),R("")},X=()=>{C(!1),v(null),k(""),R("")},Q=e.length,H=e.filter(e=>e.actif).length,G=e.filter(e=>"admin"===e.role||"super_admin"===e.role).length,K=e.filter(e=>"employee"===e.role).length;return r.jsxs(a,{children:[r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[r.jsx(o,{variant:"h4",children:"Gestion des Utilisateurs"}),N.canManageUsers&&r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>W(),children:"Nouvel Utilisateur"})]}),A&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:A}),T&&r.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>k(""),children:T}),r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Utilisateurs"}),r.jsx(o,{variant:"h6",children:Q})]}),r.jsx(E,{color:"primary",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Utilisateurs Actifs"}),r.jsx(o,{variant:"h6",color:"success.main",children:H})]}),r.jsx(st,{color:"success",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Administrateurs"}),r.jsx(o,{variant:"h6",color:"warning.main",children:G})]}),r.jsx(ot,{color:"warning",sx:{fontSize:40}})]})})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsx(te,{children:r.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[r.jsxs(a,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Employés"}),r.jsx(o,{variant:"h6",color:"info.main",children:K})]}),r.jsx(lt,{color:"info",sx:{fontSize:40}})]})})})})]}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,md:4,children:r.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom ou email...",value:s,onChange:e=>d(e.target.value),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Fe,{})})}})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Rôle"}),r.jsxs(Ce,{value:u,label:"Rôle",onChange:e=>m(e.target.value),children:[r.jsx(J,{value:"",children:"Tous les rôles"}),r.jsx(J,{value:"super_admin",children:"Super Admin"}),r.jsx(J,{value:"admin",children:"Administrateur"}),r.jsx(J,{value:"employee",children:"Employé"})]})]})}),r.jsx(ce,{item:!0,xs:12,md:3,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Statut"}),r.jsxs(Ce,{value:h,label:"Statut",onChange:e=>p(e.target.value),children:[r.jsx(J,{value:"",children:"Tous les statuts"}),r.jsx(J,{value:"active",children:"Actif"}),r.jsx(J,{value:"inactive",children:"Inactif"})]})]})})]})}),r.jsxs(me,{component:c,children:[r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Utilisateur"}),r.jsx(ge,{children:"Email"}),r.jsx(ge,{align:"center",children:"Rôle"}),r.jsx(ge,{align:"center",children:"Statut"}),r.jsx(ge,{children:"Date de Création"}),N.canManageUsers&&r.jsx(ge,{align:"center",children:"Actions"})]})}),r.jsx(ye,{children:(-1===y?n:n.slice(x*y,x*y+y)).map(n=>r.jsxs(xe,{hover:!0,onClick:()=>W(n),sx:{cursor:"pointer"},children:[r.jsx(ge,{children:r.jsxs(a,{display:"flex",alignItems:"center",gap:2,children:[r.jsx(l,{sx:{bgcolor:$(n.role)},children:n.nom.charAt(0).toUpperCase()}),r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",children:n.nom}),n.id===(null==L?void 0:L.id)&&r.jsx(M,{label:"Vous",size:"small",color:"primary"})]})]})}),r.jsx(ge,{children:n.email}),r.jsx(ge,{align:"center",children:r.jsx(M,{icon:z(n.role),label:_(n.role),color:$(n.role),size:"small"})}),r.jsx(ge,{align:"center",children:r.jsx(M,{label:n.actif?"Actif":"Inactif",color:n.actif?"success":"error",size:"small"})}),r.jsx(ge,{children:Ut(new Date(n.dateCreation),"dd/MM/yyyy",{locale:Da})}),N.canManageUsers&&r.jsx(ge,{align:"center",children:r.jsxs(a,{display:"flex",gap:1,children:[r.jsx(F,{title:"Modifier",children:r.jsx(U,{size:"small",onClick:()=>W(n),disabled:"super_admin"===n.role&&!ha.hasRole(["super_admin"]),children:r.jsx(Ae,{fontSize:"small"})})}),r.jsx(F,{title:n.actif?"Désactiver":"Activer",children:r.jsx(U,{size:"small",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id)&&n.actif)return k("Vous ne pouvez pas désactiver votre propre compte"),void setTimeout(()=>k(""),3e3);const r={...n,actif:!n.actif},a=e.map(e=>e.id===n.id?r:e);t(a),await ma.setUsers(a),R(`Utilisateur ${r.actif?"activé":"désactivé"} avec succès`),setTimeout(()=>R(""),3e3)})(n),disabled:n.id===(null==L?void 0:L.id)&&n.actif,children:n.actif?r.jsx(Y,{fontSize:"small"}):r.jsx(st,{fontSize:"small"})})}),ha.hasRole(["super_admin"])&&r.jsx(F,{title:"Supprimer",children:r.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id))return k("Vous ne pouvez pas supprimer votre propre compte"),void setTimeout(()=>k(""),3e3);if("super_admin"===n.role&&!ha.hasRole(["super_admin"]))return k("Seul un Super Admin peut supprimer un compte Super Admin"),void setTimeout(()=>k(""),3e3);if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${n.nom}" ?`)){const r=e.filter(e=>e.id!==n.id);t(r),await ma.setUsers(r),R("Utilisateur supprimé avec succès"),setTimeout(()=>R(""),3e3)}})(n),disabled:n.id===(null==L?void 0:L.id),children:r.jsx(q,{fontSize:"small"})})})]})})]},n.id))})]}),r.jsx(Re,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===y?n.length:y,page:-1===y?0:x,onPageChange:(e,t)=>{-1!==y&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);j(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),r.jsxs(Me,{open:D,onClose:X,maxWidth:"sm",fullWidth:!0,children:[r.jsx(Ie,{children:S?"Modifier l'Utilisateur":"Nouvel Utilisateur"}),r.jsxs(Ne,{children:[T&&r.jsx(O,{severity:"error",sx:{mb:2},children:T}),A&&r.jsx(O,{severity:"success",sx:{mb:2},children:A}),r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Nom complet *",value:w.nom,onChange:e=>P({...w,nom:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Email *",type:"email",value:w.email,onChange:e=>P({...w,email:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Rôle *"}),r.jsxs(Ce,{value:w.role,label:"Rôle *",onChange:e=>P({...w,role:e.target.value}),disabled:!ha.hasRole(["super_admin"])||!(!S||S.id!==(null==L?void 0:L.id)),children:[r.jsx(J,{value:"employee",children:"Employé"}),r.jsx(J,{value:"admin",children:"Administrateur"}),ha.hasRole(["super_admin"])&&r.jsx(J,{value:"super_admin",children:"Super Admin"})]})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:S?"Nouveau mot de passe (optionnel)":"Mot de passe *",type:b?"text":"password",value:w.motDePasse,onChange:e=>P({...w,motDePasse:e.target.value}),InputProps:{endAdornment:r.jsx(re,{position:"end",children:r.jsx(U,{onClick:()=>f(!b),edge:"end",children:b?r.jsx(ie,{}):r.jsx(se,{})})})},helperText:S?"Laissez vide pour conserver le mot de passe actuel":"Minimum 6 caractères"})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(Xe,{control:r.jsx(Ke,{checked:w.actif,onChange:e=>P({...w,actif:e.target.checked}),disabled:!(!S||S.id!==(null==L?void 0:L.id))}),label:"Compte actif"})})]})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:X,children:"Annuler"}),r.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void k("Le nom est requis");if(!w.email.trim())return void k("L'email est requis");if(!S&&!w.motDePasse.trim())return void k("Le mot de passe est requis pour un nouvel utilisateur");if(w.motDePasse&&w.motDePasse.length<6)return void k("Le mot de passe doit contenir au moins 6 caractères");if(e.some(e=>e.email.toLowerCase()===w.email.trim().toLowerCase()&&e.id!==(null==S?void 0:S.id)))return void k("Un utilisateur avec cet email existe déjà");if("super_admin"===w.role&&!ha.hasRole(["super_admin"]))return void k("Seul un Super Admin peut créer ou modifier un compte Super Admin");if(S&&S.id===(null==L?void 0:L.id)&&w.role!==S.role)return void k("Vous ne pouvez pas modifier votre propre rôle");if(S&&S.id===(null==L?void 0:L.id)&&!w.actif)return void k("Vous ne pouvez pas désactiver votre propre compte");const n=(new Date).toISOString();if(S){const n={...S,nom:w.nom.trim(),email:w.email.trim(),role:w.role,actif:w.actif,...w.motDePasse&&{motDePasse:w.motDePasse}},r=e.map(e=>e.id===S.id?n:e);t(r),await ma.setUsers(r),S.id===(null==L?void 0:L.id)&&await ma.setCurrentUser(n),R("Utilisateur mis à jour avec succès")}else{const r={id:Date.now().toString(),nom:w.nom.trim(),email:w.email.trim(),role:w.role,motDePasse:w.motDePasse,dateCreation:n,actif:w.actif},a=[...e,r];t(a),await ma.setUsers(a),R("Utilisateur créé avec succès")}setTimeout(()=>{X()},1500)},variant:"contained",children:S?"Mettre à jour":"Créer"})]})]})]})};class ui{async exportAllData(){try{const[e,t,n,r,a,i]=await Promise.all([ma.getProducts(),ma.getUsers(),ma.getSales(),ma.getDebts(),ma.getExpenses(),ma.getSettings()]),s={exportDate:(new Date).toISOString(),products:Or.arrayToCSV(e,Vr),users:Or.arrayToCSV(t,qr),sales:Or.arrayToCSV(n,Br),debts:Or.arrayToCSV(r,_r),expenses:Or.arrayToCSV(a,$r),settings:Or.arrayToCSV(Qr(i),Xr)},o=`SmartBoutique - Sauvegarde Complète\nDate d'exportation: ${s.exportDate}\n\n=== PRODUITS ===\n${s.products}\n\n=== UTILISATEURS ===\n${s.users}\n\n=== VENTES ===\n${s.sales}\n\n=== DETTES ===\n${s.debts}\n\n=== DÉPENSES ===\n${s.expenses}\n\n=== PARAMÈTRES ===\n${s.settings}\n`;return{success:!0,message:"Exportation complète réussie (compatible Excel)",data:"\ufeff"+o}}catch(fi){return console.error("Erreur lors de l'exportation complète:",fi),{success:!1,message:"Erreur lors de l'exportation: "+fi.message}}}async exportData(e){try{let t=[],n=[],r="";switch(e){case"products":t=await ma.getProducts(),n=Vr,r="produits";break;case"users":t=await ma.getUsers(),n=qr,r="utilisateurs";break;case"sales":t=await ma.getSales(),n=Br,r="ventes";break;case"debts":t=await ma.getDebts(),n=_r,r="dettes";break;case"expenses":t=await ma.getExpenses(),n=$r,r="depenses"}const a=Or.arrayToCSV(t,n);return{success:!0,message:`Exportation ${r} réussie (${t.length} enregistrements)`,data:a}}catch(fi){return console.error(`Erreur lors de l'exportation ${e}:`,fi),{success:!1,message:"Erreur lors de l'exportation: "+fi.message}}}async importProducts(e,t=!1){try{const n=Or.csvToArray(e,Vr),r=Or.validateCSVData(n,Vr);if(!r.isValid)return{success:!1,message:"Données invalides détectées",errors:r.errors,importedCount:0};let a=n;if(!t){const e=await ma.getProducts(),t=new Set(e.map(e=>e.id)),r=n.filter(e=>!t.has(e.id));a=[...e,...r]}return await ma.setProducts(a),{success:!0,message:`${n.length} produits importés avec succès`,errors:[],importedCount:n.length}}catch(fi){return console.error("Erreur lors de l'importation des produits:",fi),{success:!1,message:"Erreur lors de l'importation: "+fi.message,errors:[fi.message],importedCount:0}}}async importUsers(e,t=!1){try{const n=Or.csvToArray(e,qr),r=Or.validateCSVData(n,qr);if(!r.isValid)return{success:!1,message:"Données invalides détectées",errors:r.errors,importedCount:0};let a=n;if(!t){const e=await ma.getUsers(),t=new Set(e.map(e=>e.id)),r=n.filter(e=>!t.has(e.id));a=[...e,...r]}return await ma.setUsers(a),{success:!0,message:`${n.length} utilisateurs importés avec succès`,errors:[],importedCount:n.length}}catch(fi){return console.error("Erreur lors de l'importation des utilisateurs:",fi),{success:!1,message:"Erreur lors de l'importation: "+fi.message,errors:[fi.message],importedCount:0}}}generateTemplate(e){switch(e){case"products":return Or.generateTemplate(Vr);case"users":return Or.generateTemplate(qr);case"sales":return Or.generateTemplate(Br);case"debts":return Or.generateTemplate(_r);case"expenses":return Or.generateTemplate($r);default:return""}}async createAutomaticBackup(){try{const e=await this.exportAllData();if(e.success&&e.data){const t=(new Date).toISOString().replace(/[:.]/g,"-");return{success:!0,message:`Sauvegarde automatique créée: ${t}`,data:e.data}}return e}catch(fi){return console.error("Erreur lors de la sauvegarde automatique:",fi),{success:!1,message:"Erreur lors de la sauvegarde automatique: "+fi.message}}}getSampleCSVData(e){switch(e){case"products":return"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\nSAMPLE1,Produit Exemple,Description du produit exemple,5600,2,SAMPLE123,Alimentation,50,10,1234567890,2024-01-01,2024-01-01";case"users":return"ID,Nom,Email,Rôle,Mot de Passe,Date de Création,Actif\nSAMPLE1,Utilisateur Exemple,<EMAIL>,employee,motdepasse123,2024-01-01,Oui";default:return this.generateTemplate(e)}}}const mi=new ui,hi=Object.freeze(Object.defineProperty({__proto__:null,CSVImportExportService:ui,csvImportExportService:mi},Symbol.toStringTag,{value:"Module"})),pi=({onSuccess:e,onError:t})=>{var n,i,s,l;const[c,d]=vt.useState(!1),[u,m]=vt.useState(!1),[p,x]=vt.useState(!1),[g,y]=vt.useState("products"),[j,D]=vt.useState(""),[C,S]=vt.useState(!1),[v,b]=vt.useState(""),f=[{key:"products",label:"Produits",icon:"📦"},{key:"users",label:"Utilisateurs",icon:"👥"},{key:"sales",label:"Ventes",icon:"💰"},{key:"debts",label:"Dettes",icon:"📋"},{key:"expenses",label:"Dépenses",icon:"💸"}],w=()=>{const e=mi.generateTemplate(g);D(e)};return r.jsx(ee,{children:r.jsxs(te,{children:[r.jsxs(o,{variant:"h6",gutterBottom:!0,children:[r.jsx(ct,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Données CSV"]}),r.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Exportez et importez vos données au format CSV pour une meilleure portabilité et accessibilité."}),r.jsxs(ce,{container:!0,spacing:2,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(a,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[r.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[r.jsx(Ue,{sx:{mr:1,verticalAlign:"middle"}}),"Exportation"]}),r.jsx(I,{variant:"contained",color:"primary",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await mi.exportAllData();if(n.success&&n.data){const t=new Blob([n.data],{type:"text/plain;charset=utf-8"}),r=URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.txt`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),null==e||e("Sauvegarde complète exportée avec succès (compatible Excel)")}else null==t||t(n.message)}catch(fi){null==t||t("Erreur lors de l'exportation: "+fi.message)}finally{d(!1)}},disabled:c,startIcon:c?r.jsx(qe,{size:20}):r.jsx(dt,{}),sx:{mb:2},children:"Exporter Toutes les Données"}),r.jsx(h,{sx:{my:2}}),r.jsx(o,{variant:"body2",gutterBottom:!0,children:"Exporter un type de données spécifique:"}),r.jsx(a,{sx:{mb:2},children:f.map(e=>r.jsx(M,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),r.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await mi.exportData(g);n.success&&n.data?(b(n.data),m(!0),null==e||e(n.message)):null==t||t(n.message)}catch(fi){null==t||t("Erreur lors de l'exportation: "+fi.message)}finally{d(!1)}},disabled:c,startIcon:r.jsx(ut,{}),children:["Exporter ",null==(n=f.find(e=>e.key===g))?void 0:n.label]})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(a,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[r.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[r.jsx(Te,{sx:{mr:1,verticalAlign:"middle"}}),"Importation"]}),r.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Sélectionnez le type de données à importer:"}),r.jsx(a,{sx:{mb:2},children:f.filter(e=>["products","users"].includes(e.key)).map(e=>r.jsx(M,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),r.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:()=>x(!0),startIcon:r.jsx(Te,{}),sx:{mb:1},children:["Importer ",null==(i=f.find(e=>e.key===g))?void 0:i.label]}),r.jsx(I,{variant:"text",size:"small",fullWidth:!0,onClick:w,children:"Obtenir un modèle CSV"})]})})]}),r.jsxs(Me,{open:u,onClose:()=>m(!1),maxWidth:"md",fullWidth:!0,children:[r.jsxs(Ie,{children:["Données Exportées - ",null==(s=f.find(e=>e.key===g))?void 0:s.label]}),r.jsx(Ne,{children:r.jsx(ne,{multiline:!0,rows:10,fullWidth:!0,value:v,variant:"outlined",InputProps:{readOnly:!0},sx:{fontFamily:"monospace"}})}),r.jsxs(Le,{children:[r.jsx(I,{onClick:()=>m(!1),children:"Fermer"}),r.jsx(I,{onClick:()=>{var e;const t=(null==(e=f.find(e=>e.key===g))?void 0:e.label)||g;let n=v;n.startsWith("\ufeff")||(n="\ufeff"+n);const r=new Blob([n],{type:"text/csv;charset=utf-8"}),a=URL.createObjectURL(r),i=document.createElement("a");i.href=a,i.download=`SmartBoutique_${t}_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a),m(!1)},variant:"contained",startIcon:r.jsx(Ue,{}),children:"Télécharger CSV"})]})]}),r.jsxs(Me,{open:p,onClose:()=>x(!1),maxWidth:"md",fullWidth:!0,children:[r.jsxs(Ie,{children:["Importer ",null==(l=f.find(e=>e.key===g))?void 0:l.label]}),r.jsxs(Ne,{children:[r.jsx(O,{severity:"info",sx:{mb:2},children:'Collez le contenu CSV ci-dessous. Utilisez "Obtenir un modèle CSV" pour voir le format requis.'}),r.jsx(ne,{multiline:!0,rows:8,fullWidth:!0,value:j,onChange:e=>D(e.target.value),placeholder:"Collez votre contenu CSV ici...",variant:"outlined",sx:{mb:2,fontFamily:"monospace"}}),r.jsx(Xe,{control:r.jsx(Je,{checked:C,onChange:e=>S(e.target.checked)}),label:"Remplacer les données existantes"})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:()=>x(!1),children:"Annuler"}),r.jsx(I,{onClick:w,variant:"outlined",children:"Obtenir Modèle"}),r.jsx(I,{onClick:async()=>{if(j.trim()){d(!0);try{let n;switch(g){case"products":n=await mi.importProducts(j,C);break;case"users":n=await mi.importUsers(j,C);break;default:return void(null==t||t("Type de données non supporté pour l'importation"))}n.success?(null==e||e(n.message),x(!1),D("")):null==t||t(n.message+"\n"+n.errors.join("\n"))}catch(fi){null==t||t("Erreur lors de l'importation: "+fi.message)}finally{d(!1)}}else null==t||t("Veuillez saisir le contenu CSV à importer")},variant:"contained",disabled:c||!j.trim(),startIcon:c?r.jsx(qe,{size:20}):r.jsx(Te,{}),children:"Importer"})]})]})]})})},xi=({value:e="",onChange:t,disabled:n=!1,maxSizeKB:i=500,acceptedFormats:s=["image/jpeg","image/jpg","image/png","image/gif"]})=>{const[l,c]=vt.useState(""),[d,u]=vt.useState(!1),m=vt.useRef(null),h=()=>{m.current&&m.current.click()};return r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Logo de l'entreprise"}),r.jsx("input",{ref:m,type:"file",accept:s.join(","),onChange:e=>{var n;const r=null==(n=e.target.files)?void 0:n[0];if(!r)return;if(c(""),u(!0),!s.includes(r.type))return c(`Format non supporté. Formats acceptés: ${s.map(e=>e.split("/")[1].toUpperCase()).join(", ")}`),void u(!1);if(r.size/1024>i)return c(`Fichier trop volumineux. Taille maximale: ${i}KB`),void u(!1);const a=new FileReader;a.onload=e=>{var n;const r=null==(n=e.target)?void 0:n.result;r&&t(r),u(!1)},a.onerror=()=>{c("Erreur lors de la lecture du fichier"),u(!1)},a.readAsDataURL(r)},style:{display:"none"},disabled:n}),r.jsx(ee,{variant:"outlined",sx:{mb:2},children:r.jsx(te,{sx:{textAlign:"center",py:3},children:e?r.jsxs(a,{children:[r.jsx(a,{component:"img",src:e,alt:"Logo de l'entreprise",sx:{maxWidth:"200px",maxHeight:"100px",objectFit:"contain",border:"1px solid #e0e0e0",borderRadius:1,mb:2}}),r.jsxs(a,{children:[r.jsx(I,{variant:"outlined",startIcon:r.jsx(mt,{}),onClick:h,disabled:n||d,sx:{mr:1},children:"Changer"}),r.jsx(U,{color:"error",onClick:()=>{t(""),c(""),m.current&&(m.current.value="")},disabled:n||d,title:"Supprimer le logo",children:r.jsx(q,{})})]})]}):r.jsxs(a,{children:[r.jsx(ht,{sx:{fontSize:48,color:"text.secondary",mb:2}}),r.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Aucun logo configuré"}),r.jsx(I,{variant:"contained",startIcon:d?r.jsx(qe,{size:20}):r.jsx(mt,{}),onClick:h,disabled:n||d,children:d?"Chargement...":"Télécharger un logo"})]})})}),l&&r.jsx(O,{severity:"error",sx:{mb:2},children:l}),r.jsxs(o,{variant:"caption",color:"text.secondary",children:["Formats acceptés: ",s.map(e=>e.split("/")[1].toUpperCase()).join(", ")," • Taille maximale: ",i,"KB • Recommandé: 200x100px pour un affichage optimal sur les reçus thermiques"]})]})};function gi(e){const{children:t,value:n,index:i,...s}=e;return r.jsx("div",{role:"tabpanel",hidden:n!==i,id:`settings-tabpanel-${i}`,"aria-labelledby":`settings-tab-${i}`,...s,children:n===i&&r.jsx(a,{sx:{p:3},children:t})})}const yi=()=>{const[e,t]=vt.useState(null),[n,i]=vt.useState(0),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,g]=vt.useState({nom:"",adresse:"",telephone:"",email:"",rccm:"",idNat:"",logo:""}),[j,D]=vt.useState({tauxChangeUSDCDF:2800,seuilStockBas:10}),[C,S]=vt.useState({impressionAutomatique:!1,taillePapier:"thermal"}),[v,b]=vt.useState([]),[f,w]=vt.useState(!1),[E,P]=vt.useState(null),[F,T]=vt.useState({nom:"",description:"",couleur:"#2196F3"}),k=ha.getUserPermissions();vt.useEffect(()=>{A()},[]);const A=async()=>{var e,n,r,a,i,s,o,l,c,d,u,m,h,p,x,y;const j=await ma.getSettings();t(j),g({nom:(null==(e=null==j?void 0:j.company)?void 0:e.nom)||(null==(n=null==j?void 0:j.entreprise)?void 0:n.nom)||"SmartBoutique",adresse:(null==(r=null==j?void 0:j.company)?void 0:r.adresse)||(null==(a=null==j?void 0:j.entreprise)?void 0:a.adresse)||"Kinshasa, RDC",telephone:(null==(i=null==j?void 0:j.company)?void 0:i.telephone)||(null==(s=null==j?void 0:j.entreprise)?void 0:s.telephone)||"+*********** 000",email:(null==(o=null==j?void 0:j.company)?void 0:o.email)||(null==(l=null==j?void 0:j.entreprise)?void 0:l.email)||"<EMAIL>",rccm:(null==(c=null==j?void 0:j.company)?void 0:c.rccm)||(null==(d=null==j?void 0:j.entreprise)?void 0:d.rccm)||"",idNat:(null==(u=null==j?void 0:j.company)?void 0:u.idNat)||(null==(m=null==j?void 0:j.entreprise)?void 0:m.idNat)||"",logo:(null==(h=null==j?void 0:j.company)?void 0:h.logo)||(null==(p=null==j?void 0:j.entreprise)?void 0:p.logo)||""}),D({tauxChangeUSDCDF:(null==j?void 0:j.tauxChangeUSDCDF)||2800,seuilStockBas:(null==j?void 0:j.seuilStockBas)||10}),S({impressionAutomatique:(null==(x=null==j?void 0:j.impression)?void 0:x.impressionAutomatique)||!1,taillePapier:(null==(y=null==j?void 0:j.impression)?void 0:y.taillePapier)||"thermal"}),b((null==j?void 0:j.categories)||[{id:"1",nom:"Alimentation",couleur:"#4CAF50"},{id:"2",nom:"Vêtements",couleur:"#2196F3"},{id:"3",nom:"Électronique",couleur:"#FF9800"},{id:"4",nom:"Boissons",couleur:"#9C27B0"},{id:"5",nom:"Épicerie",couleur:"#795548"},{id:"6",nom:"Livres",couleur:"#607D8B"}])},R=e=>{e?(P(e),T({nom:e.nom,description:e.description,couleur:e.couleur})):(P(null),T({nom:"",description:"",couleur:"#2196F3"})),w(!0),u("")},N=()=>{w(!1),P(null),u("")},L=async()=>{try{console.log("🔄 Initializing essential user accounts only...");const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await ma.setUsers(e),await ma.setProducts([]),await ma.setSales([]),await ma.setDebts([]),await ma.setExpenses([]),await ma.setEmployeePayments([]);const t={tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Alimentation",couleur:"#4CAF50"},{id:"2",nom:"Vêtements",couleur:"#2196F3"},{id:"3",nom:"Électronique",couleur:"#FF9800"},{id:"4",nom:"Boissons",couleur:"#9C27B0"},{id:"5",nom:"Épicerie",couleur:"#795548"},{id:"6",nom:"Livres",couleur:"#607D8B"}],company:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}};await ma.setSettings(t),console.log("✅ Essential user accounts initialized successfully (no demo data)")}catch(e){throw console.error("❌ Error initializing essential users:",e),e}},B=async()=>{try{(()=>{var e,t;return"capacitor:"===window.location.protocol||(null==(t=null==(e=window.Capacitor)?void 0:e.isNativePlatform)?void 0:t.call(e))||navigator.userAgent.includes("Capacitor")})()?await _():await $(),console.log("✅ Complete data reset performed successfully")}catch(e){throw console.error("❌ Error during data reset:",e),e}},_=async()=>{try{const{Preferences:t}=await br(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Gr);return{Preferences:e}},void 0,import.meta.url),{keys:n}=await t.keys(),r=n.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const e of r)await t.remove({key:e});try{const{mobileSQLiteStorageService:e}=await br(async()=>{const{mobileSQLiteStorageService:e}=await Promise.resolve().then(()=>da);return{mobileSQLiteStorageService:e}},void 0,import.meta.url);console.log("Mobile SQLite data cleared (if available)")}catch(e){console.log("Mobile SQLite not available or already cleared")}console.log("✅ Mobile data cleared successfully")}catch(e){throw console.error("❌ Error clearing mobile data:",e),e}},$=async()=>{try{Object.keys(localStorage).forEach(e=>{e.startsWith("smartboutique_")&&localStorage.removeItem(e)});try{const{sqliteStorageService:e}=await br(async()=>{const{sqliteStorageService:e}=await Promise.resolve().then(()=>aa);return{sqliteStorageService:e}},void 0,import.meta.url);console.log("Desktop SQLite data cleared (if available)")}catch(e){console.log("Desktop SQLite not available or already cleared")}console.log("✅ Desktop data cleared successfully")}catch(e){throw console.error("❌ Error clearing desktop data:",e),e}};return e?r.jsxs(a,{children:[r.jsx(o,{variant:"h4",gutterBottom:!0,children:"Paramètres"}),s&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>l(""),children:s}),d&&r.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>u(""),children:d}),r.jsx(c,{sx:{mb:3},children:r.jsxs(at,{value:n,onChange:(e,t)=>{i(t)},"aria-label":"settings tabs",children:[r.jsx(it,{label:"Entreprise",icon:r.jsx(pt,{})}),r.jsx(it,{label:"Général",icon:r.jsx(de,{})}),r.jsx(it,{label:"Impression",icon:r.jsx(Be,{})}),r.jsx(it,{label:"Catégories",icon:r.jsx(nt,{})}),r.jsx(it,{label:"Sauvegarde",icon:r.jsx(dt,{})}),r.jsx(it,{label:"Données CSV",icon:r.jsx(Ue,{})})]})}),r.jsx(gi,{value:n,index:0,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Informations de l'Entreprise",avatar:r.jsx(pt,{})}),r.jsx(te,{children:r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Nom de l'entreprise",value:m.nom,onChange:e=>g({...m,nom:e.target.value}),disabled:!k.canManageSettings})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:m.email,onChange:e=>g({...m,email:e.target.value}),disabled:!k.canManageSettings})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Téléphone",value:m.telephone,onChange:e=>g({...m,telephone:e.target.value}),disabled:!k.canManageSettings})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Adresse",value:m.adresse,onChange:e=>g({...m,adresse:e.target.value}),disabled:!k.canManageSettings})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"RCCM",value:m.rccm||"",onChange:e=>g({...m,rccm:e.target.value}),disabled:!k.canManageSettings,helperText:"Registre de Commerce et du Crédit Mobilier"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"ID NAT",value:m.idNat||"",onChange:e=>g({...m,idNat:e.target.value}),disabled:!k.canManageSettings,helperText:"Identification Nationale"})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(xi,{value:m.logo||"",onChange:e=>g({...m,logo:e}),disabled:!k.canManageSettings})}),k.canManageSettings&&r.jsx(ce,{item:!0,xs:12,children:r.jsx(I,{variant:"contained",startIcon:r.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,entreprise:m};await ma.setSettings(n),t(n),l("Paramètres de l'entreprise sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),r.jsx(gi,{value:n,index:1,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Paramètres Généraux",avatar:r.jsx(de,{})}),r.jsx(te,{children:r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,children:r.jsx(Ya,{label:"Taux de change (1 USD = ? CDF)",value:j.tauxChangeUSDCDF,onChange:e=>D({...j,tauxChangeUSDCDF:e}),min:1e3,max:1e4,step:10,exchangeRate:j.tauxChangeUSDCDF,disabled:!k.canManageSettings,showSlider:!0,allowUSDInput:!1,helperText:"Définit le taux de conversion entre USD et CDF"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Seuil de stock bas",type:"number",value:j.seuilStockBas,onChange:e=>D({...j,seuilStockBas:parseInt(e.target.value)||0}),disabled:!k.canManageSettings,helperText:"Alerte quand le stock est ≤ à cette valeur"})}),k.canManageSettings&&r.jsx(ce,{item:!0,xs:12,children:r.jsx(I,{variant:"contained",startIcon:r.jsx(xt,{}),onClick:async()=>{if(!e)return;if(j.tauxChangeUSDCDF<=0)return void u("Le taux de change doit être supérieur à 0");if(j.seuilStockBas<0)return void u("Le seuil de stock bas ne peut pas être négatif");const n={...e,tauxChangeUSDCDF:j.tauxChangeUSDCDF,seuilStockBas:j.seuilStockBas};await ma.setSettings(n),t(n),l("Paramètres généraux sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),r.jsx(gi,{value:n,index:2,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Paramètres d'Impression",avatar:r.jsx(Be,{})}),r.jsx(te,{children:r.jsxs(ce,{container:!0,spacing:3,children:[r.jsxs(ce,{item:!0,xs:12,children:[r.jsx(Xe,{control:r.jsx(Ke,{checked:C.impressionAutomatique,onChange:e=>S({...C,impressionAutomatique:e.target.checked}),disabled:!k.canManageSettings}),label:"Impression automatique des reçus"}),r.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Lorsque activé, les reçus seront automatiquement imprimés après chaque vente ou dépense"})]}),r.jsxs(ce,{item:!0,xs:12,md:6,children:[r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Taille du papier"}),r.jsxs(Ce,{value:C.taillePapier,label:"Taille du papier",onChange:e=>S({...C,taillePapier:e.target.value}),disabled:!k.canManageSettings,children:[r.jsx(J,{value:"thermal",children:"Reçu thermique (80mm)"}),r.jsx(J,{value:"a4",children:"A4"})]})]}),r.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Choisissez le format de papier pour l'impression des reçus"})]}),k.canManageSettings&&r.jsx(ce,{item:!0,xs:12,children:r.jsx(I,{variant:"contained",startIcon:r.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,impression:{impressionAutomatique:C.impressionAutomatique,taillePapier:C.taillePapier}};await ma.setSettings(n),t(n),l("Paramètres d'impression sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),r.jsx(gi,{value:n,index:3,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Gestion des Catégories",avatar:r.jsx(nt,{}),action:k.canManageSettings&&r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>R(),children:"Nouvelle Catégorie"})}),r.jsx(te,{children:r.jsx(p,{children:v.map((n,i)=>r.jsxs(ft.Fragment,{children:[r.jsxs(x,{children:[r.jsx(a,{sx:{width:20,height:20,backgroundColor:n.couleur,borderRadius:1,mr:2}}),r.jsx(y,{primary:n.nom,secondary:n.description}),k.canManageSettings&&r.jsxs(V,{children:[r.jsx(U,{edge:"end",onClick:()=>R(n),sx:{mr:1},children:r.jsx(Ae,{})}),r.jsx(U,{edge:"end",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${n.nom}" ?`)){if((await ma.getProducts()).some(e=>e.categorie===n.nom))return u("Cette catégorie est utilisée par des produits et ne peut pas être supprimée"),void setTimeout(()=>u(""),5e3);const r=v.filter(e=>e.id!==n.id);if(b(r),e){const n={...e,categories:r};await ma.setSettings(n),t(n)}l("Catégorie supprimée avec succès"),setTimeout(()=>l(""),3e3)}})(n),children:r.jsx(q,{})})]})]}),i<v.length-1&&r.jsx(h,{})]},n.id))})})]})}),r.jsx(gi,{value:n,index:4,children:r.jsxs(ce,{container:!0,spacing:3,children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Sauvegarde des Données",avatar:r.jsx(Ue,{})}),r.jsxs(te,{children:[r.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Exportez toutes vos données dans un fichier JSON pour créer une sauvegarde."}),r.jsx(I,{variant:"contained",startIcon:r.jsx(Ue,{}),onClick:async()=>{try{const{csvImportExportService:e}=await br(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>hi);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();if(t.success&&t.data){const e=new Blob([t.data],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(e),r=document.createElement("a");r.href=n,r.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n),l("Sauvegarde CSV exportée avec succès (compatible Excel)"),setTimeout(()=>l(""),3e3)}else u(t.message||"Erreur lors de l'exportation"),setTimeout(()=>u(""),3e3)}catch(e){u("Erreur lors de l'exportation des données"),setTimeout(()=>u(""),3e3)}},fullWidth:!0,sx:{mt:2},children:"Exporter les Données"})]})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Restauration des Données",avatar:r.jsx(Te,{})}),r.jsxs(te,{children:[r.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Importez un fichier de sauvegarde pour restaurer vos données."}),r.jsx("input",{accept:".json",style:{display:"none"},id:"import-file",type:"file",onChange:async e=>{var t;const n=null==(t=e.target.files)?void 0:t[0];if(!n)return;const r=new FileReader;r.onload=async e=>{var t;try{const n=JSON.parse(null==(t=e.target)?void 0:t.result);await ma.importData(n)?(A(),l("Données importées avec succès"),setTimeout(()=>l(""),3e3),setTimeout(()=>window.location.reload(),2e3)):(u("Erreur lors de l'importation des données"),setTimeout(()=>u(""),3e3))}catch(n){u("Fichier de sauvegarde invalide"),setTimeout(()=>u(""),3e3)}},r.readAsText(n),e.target.value=""}}),r.jsx("label",{htmlFor:"import-file",children:r.jsx(I,{variant:"outlined",component:"span",startIcon:r.jsx(Te,{}),fullWidth:!0,sx:{mt:2},children:"Importer les Données"})})]})]})}),ha.hasRole(["super_admin"])&&r.jsx(ce,{item:!0,xs:12,children:r.jsxs(ee,{children:[r.jsx(ue,{title:"Réinitialisation",avatar:r.jsx(gt,{})}),r.jsxs(te,{children:[r.jsx(O,{severity:"warning",sx:{mb:2},children:r.jsxs(o,{variant:"body2",children:[r.jsx("strong",{children:"Attention:"})," Cette action supprimera toutes les données et restaurera les paramètres par défaut. Cette action est irréversible."]})}),r.jsx(I,{variant:"outlined",color:"error",startIcon:r.jsx(gt,{}),onClick:async()=>{if(!window.confirm("Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action supprimera définitivement :"))return;if(window.confirm("ATTENTION: Cette action va supprimer TOUTES les données suivantes :\n\n• Tous les produits et inventaire\n• Toutes les ventes et transactions\n• Toutes les dettes et paiements\n• Tous les employés et leurs paiements\n• Toutes les dépenses\n• Tous les utilisateurs (sauf admin système)\n• Tous les fichiers CSV et données importées\n\nL'application sera nettoyée et prête pour vos propres données.\n\nCette action est IRRÉVERSIBLE. Confirmez-vous ?"))try{u(""),l("Réinitialisation en cours... Veuillez patienter.");const e=await ma.getCurrentUser(),t=(null==e?void 0:e.email)||"<EMAIL>";await B(),await L(),localStorage.setItem("smartboutique_data_reset","true");const n=await ma.getUsers(),r=n.find(e=>e.email===t)||n.find(e=>"super_admin"===e.role);r&&await ma.setCurrentUser(r),l("✅ Données réinitialisées avec succès ! Application prête pour vos données."),setTimeout(()=>{window.location.reload()},2e3)}catch(e){console.error("Erreur lors de la réinitialisation:",e),u("Erreur lors de la réinitialisation des données. Veuillez réessayer."),setTimeout(()=>u(""),5e3)}},children:"Réinitialiser toutes les Données"})]})]})})]})}),r.jsx(gi,{value:n,index:5,children:r.jsx(pi,{onSuccess:e=>{l(e),setTimeout(()=>l(""),3e3)},onError:e=>{u(e),setTimeout(()=>u(""),5e3)}})}),r.jsxs(Me,{open:f,onClose:N,maxWidth:"sm",fullWidth:!0,children:[r.jsx(Ie,{children:E?"Modifier la Catégorie":"Nouvelle Catégorie"}),r.jsxs(Ne,{children:[d&&r.jsx(O,{severity:"error",sx:{mb:2},children:d}),r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Nom de la catégorie *",value:F.nom,onChange:e=>T({...F,nom:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:2,value:F.description,onChange:e=>T({...F,description:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Couleur",type:"color",value:F.couleur,onChange:e=>T({...F,couleur:e.target.value}),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(yt,{})})}})}),r.jsx(ce,{item:!0,xs:12,children:r.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[r.jsx(o,{variant:"body2",children:"Aperçu:"}),r.jsx(M,{label:F.nom||"Nom de la catégorie",sx:{backgroundColor:F.couleur,color:"white"}})]})})]})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:N,children:"Annuler"}),r.jsx(I,{onClick:async()=>{if(!F.nom.trim())return void u("Le nom de la catégorie est requis");if(v.some(e=>e.nom.toLowerCase()===F.nom.trim().toLowerCase()&&e.id!==(null==E?void 0:E.id)))return void u("Une catégorie avec ce nom existe déjà");let n;if(E)n=v.map(e=>e.id===E.id?{...e,nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur}:e);else{const e={id:Date.now().toString(),nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur};n=[...v,e]}if(b(n),e){const r={...e,categories:n};await ma.setSettings(r),t(r)}l(E?"Catégorie mise à jour":"Catégorie créée"),setTimeout(()=>l(""),3e3),N()},variant:"contained",children:E?"Mettre à jour":"Créer"})]})]})]}):r.jsx(o,{children:"Chargement..."})},ji=()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState([]),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,h]=vt.useState(""),[p,x]=vt.useState(0),[g,y]=vt.useState(10),[j,D]=vt.useState(!1),[C,S]=vt.useState(null),[v,b]=vt.useState({nomEmploye:"",montantCDF:0,datePaiement:Ut(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""}),[f,w]=vt.useState(""),[E,P]=vt.useState(""),[T,k]=vt.useState({tauxChangeUSDCDF:2800}),[A,R]=vt.useState(!1),[N,L]=vt.useState([]),[V,B]=vt.useState(!1),[_,$]=vt.useState(null),[z,W]=vt.useState({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ut(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""}),[X,Q]=vt.useState(0),H=ha.getUserPermissions(),Y=ha.getCurrentUser(),K=[{value:"cash",label:"Cash"},{value:"mobile_money",label:"Mobile Money"},{value:"bank",label:"Banque"}];vt.useEffect(()=>{Z(),ae(),ie()},[]),vt.useEffect(()=>{se()},[e,s,d,m]);const Z=async()=>{try{R(!0);const e=await ma.getEmployeePayments();t(e)}catch(e){console.error("Error loading employee payments:",e),w("Erreur lors du chargement des paiements employés")}finally{R(!1)}},ae=async()=>{try{const e=await ma.getEmployees();L(e)}catch(e){console.error("Error loading employees:",e),w("Erreur lors du chargement des employés")}},ie=async()=>{try{const e=await ma.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}},se=()=>{let t=[...e];if(s&&(t=t.filter(e=>{var t;return e.nomEmploye.toLowerCase().includes(s.toLowerCase())||(null==(t=e.notes)?void 0:t.toLowerCase().includes(s.toLowerCase()))})),d&&(t=t.filter(e=>e.methodePaiement===d)),m){const e=new Date;let n,r=e;switch(m){case"today":n=new Date(e.getFullYear(),e.getMonth(),e.getDate());break;case"week":n=new Date(e.getTime()-6048e5);break;case"month":n=It(e),r=Nt(e);break;case"year":n=new Date(e.getFullYear(),0,1);break;default:n=new Date(0)}t=t.filter(e=>{const t=new Date(e.datePaiement);return Ot(t,{start:n,end:r})})}i(t),x(0)},oe=e=>{e?(S(e),b({nomEmploye:e.nomEmploye,montantCDF:e.montantCDF,datePaiement:e.datePaiement,methodePaiement:e.methodePaiement,notes:e.notes||""})):(S(null),b({nomEmploye:"",montantCDF:0,datePaiement:Ut(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""})),D(!0),w(""),P("")},le=()=>{D(!1),S(null),w(""),P("")},de=e=>{const t=K.find(t=>t.value===e);return t?t.label:e},ue=e=>{e?($(e),W({nomComplet:e.nomComplet,poste:e.poste,salaireCDF:e.salaireCDF,dateEmbauche:e.dateEmbauche,telephone:e.telephone||"",adresse:e.adresse||"",statut:e.statut,notes:e.notes||""})):($(null),W({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ut(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""})),B(!0),w(""),P("")},Se=()=>{B(!1),$(null),w(""),P("")},ve=n.reduce((e,t)=>e+t.montantCDF,0);return n.reduce((e,t)=>e+(t.montantUSD||0),0),r.jsxs(a,{p:3,children:[r.jsxs(o,{variant:"h4",gutterBottom:!0,children:[r.jsx(G,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Employés et Paiements"]}),r.jsx(c,{sx:{mb:3},children:r.jsxs(at,{value:X,onChange:(e,t)=>Q(t),children:[r.jsx(it,{label:"Paiements Employés"}),r.jsx(it,{label:"Gestion des Employés"})]})}),0===X&&r.jsxs(r.Fragment,{children:[r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Paiements"}),r.jsx(o,{variant:"h5",children:n.length})]})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Montant Total"}),r.jsxs(o,{variant:"h5",fontWeight:"medium",children:[_a(ve,T.tauxChangeUSDCDF).primaryAmount," ",_a(ve,T.tauxChangeUSDCDF).primaryCurrency]}),r.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_a(ve,T.tauxChangeUSDCDF).secondaryAmount]})]})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Payés"}),r.jsx(o,{variant:"h5",children:new Set(n.map(e=>e.nomEmploye)).size})]})})})]}),f&&r.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>w(""),children:f}),E&&r.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>P(""),children:E}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ne,{fullWidth:!0,label:"Rechercher employé",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(Fe,{})})}})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Méthode de paiement"}),r.jsxs(Ce,{value:d,onChange:e=>u(e.target.value),label:"Méthode de paiement",children:[r.jsx(J,{value:"",children:"Toutes"}),K.map(e=>r.jsx(J,{value:e.value,children:e.label},e.value))]})]})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Période"}),r.jsx(Ce,{value:m,onChange:e=>h(e.target.value),label:"Période",children:[{value:"",label:"Toutes les dates"},{value:"today",label:"Aujourd'hui"},{value:"week",label:"Cette semaine"},{value:"month",label:"Ce mois"},{value:"year",label:"Cette année"}].map(e=>r.jsx(J,{value:e.value,children:e.label},e.value))})]})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>oe(),disabled:!H.canManageEmployeePayments||A,fullWidth:!0,children:"Nouveau Paiement"})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:r.jsx(I,{variant:"outlined",onClick:()=>{l(""),u(""),h("")},fullWidth:!0,children:"Réinitialiser"})})]})}),r.jsxs(c,{children:[r.jsx(me,{children:r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Employé"}),r.jsx(ge,{children:"Montant (CDF)"}),r.jsx(ge,{children:"Montant (USD)"}),r.jsx(ge,{children:"Date de Paiement"}),r.jsx(ge,{children:"Méthode"}),r.jsx(ge,{children:"Notes"}),r.jsx(ge,{children:"Créé par"}),H.canManageEmployeePayments&&r.jsx(ge,{children:"Actions"})]})}),r.jsx(ye,{children:A?r.jsx(xe,{children:r.jsx(ge,{colSpan:8,align:"center",children:r.jsx(qe,{})})}):0===n.length?r.jsx(xe,{children:r.jsx(ge,{colSpan:8,align:"center",children:r.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun paiement employé trouvé"})})}):n.slice(p*g,p*g+g).map(e=>r.jsxs(xe,{children:[r.jsx(ge,{children:r.jsxs(a,{display:"flex",alignItems:"center",children:[r.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomEmploye]})}),r.jsx(ge,{children:r.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Ba(e.montantCDF,"CDF")})}),r.jsx(ge,{children:r.jsx(o,{variant:"body2",color:"textSecondary",children:Ba(e.montantUSD||0,"USD")})}),r.jsx(ge,{children:r.jsxs(a,{display:"flex",alignItems:"center",children:[r.jsx(rt,{sx:{mr:1,fontSize:16,color:"text.secondary"}}),Ut(new Date(e.datePaiement),"dd/MM/yyyy",{locale:Da})]})}),r.jsx(ge,{children:r.jsx(M,{label:de(e.methodePaiement),size:"small",color:"cash"===e.methodePaiement?"success":"mobile_money"===e.methodePaiement?"info":"default"})}),r.jsx(ge,{children:r.jsx(o,{variant:"body2",noWrap:!0,sx:{maxWidth:150},children:e.notes||"-"})}),r.jsx(ge,{children:r.jsx(o,{variant:"body2",color:"textSecondary",children:e.creePar})}),H.canManageEmployeePayments&&r.jsxs(ge,{children:[r.jsx(F,{title:"Modifier",children:r.jsx(U,{size:"small",onClick:()=>oe(e),disabled:A,children:r.jsx(Ae,{})})}),r.jsx(F,{title:"Supprimer",children:r.jsx(U,{size:"small",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le paiement de ${e.nomEmploye} ?`))try{R(!0),await ma.deleteEmployeePayment(e.id),P("Paiement employé supprimé avec succès"),await Z()}catch(t){console.error("Error deleting employee payment:",t),w("Erreur lors de la suppression du paiement employé")}finally{R(!1)}})(e),disabled:A,color:"error",children:r.jsx(q,{})})})]})]},e.id))})]})}),r.jsx(Re,{rowsPerPageOptions:[5,10,25,50,100],component:"div",count:n.length,rowsPerPage:g,page:p,onPageChange:(e,t)=>{x(t)},onRowsPerPageChange:e=>{y(parseInt(e.target.value,10)),x(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),r.jsxs(Me,{open:j,onClose:le,maxWidth:"sm",fullWidth:!0,children:[r.jsx(Ie,{children:C?"Modifier le Paiement Employé":"Nouveau Paiement Employé"}),r.jsx(Ne,{children:r.jsx(a,{sx:{pt:1},children:r.jsxs(ce,{container:!0,spacing:2,children:[r.jsx(ce,{item:!0,xs:12,children:r.jsxs(je,{fullWidth:!0,disabled:A,children:[r.jsx(De,{id:"employee-select-label",children:"Nom de l'employé *"}),r.jsxs(Ce,{labelId:"employee-select-label",value:v.nomEmploye,onChange:e=>b({...v,nomEmploye:e.target.value}),label:"Nom de l'employé *",startAdornment:r.jsx(re,{position:"start",children:r.jsx(lt,{})}),children:[r.jsx(J,{value:"",disabled:!0,children:r.jsx("em",{children:"Sélectionner un employé"})}),N.filter(e=>"actif"===e.statut).sort((e,t)=>e.nomComplet.localeCompare(t.nomComplet,"fr",{sensitivity:"base"})).map(e=>r.jsx(J,{value:e.nomComplet,children:r.jsxs(a,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[r.jsx(o,{variant:"body1",sx:{fontWeight:500},children:e.nomComplet}),r.jsx(o,{variant:"caption",color:"text.secondary",children:e.poste})]})},e.id)),0===N.filter(e=>"actif"===e.statut).length&&r.jsx(J,{value:"",disabled:!0,children:r.jsx("em",{children:"Aucun employé actif disponible"})})]})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(Ya,{label:"Montant du paiement *",value:v.montantCDF,onChange:e=>b({...v,montantCDF:e}),currency:"CDF",disabled:A,fullWidth:!0})}),r.jsx(ce,{item:!0,xs:12,sm:6,children:r.jsx(ne,{fullWidth:!0,type:"date",label:"Date de paiement *",value:v.datePaiement,onChange:e=>b({...v,datePaiement:e.target.value}),disabled:A,InputLabelProps:{shrink:!0},InputProps:{startAdornment:r.jsx(re,{position:"start",children:r.jsx(rt,{})})}})}),r.jsx(ce,{item:!0,xs:12,sm:6,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Méthode de paiement *"}),r.jsx(Ce,{value:v.methodePaiement,onChange:e=>b({...v,methodePaiement:e.target.value}),label:"Méthode de paiement *",disabled:A,children:K.map(e=>r.jsx(J,{value:e.value,children:e.label},e.value))})]})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,multiline:!0,rows:3,label:"Notes (optionnel)",value:v.notes,onChange:e=>b({...v,notes:e.target.value}),disabled:A,placeholder:"Ajoutez des notes sur ce paiement..."})}),v.montantCDF>0&&r.jsx(ce,{item:!0,xs:12,children:r.jsxs(a,{sx:{p:2,bgcolor:"grey.50",borderRadius:1},children:[r.jsxs(o,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:["Équivalent USD (taux: ",T.tauxChangeUSDCDF," CDF/USD)"]}),r.jsx(o,{variant:"h6",color:"primary",children:Ba(v.montantCDF/T.tauxChangeUSDCDF,"USD")})]})})]})})}),r.jsxs(Le,{children:[r.jsx(I,{onClick:le,disabled:A,children:"Annuler"}),r.jsx(I,{onClick:async()=>{var e;try{if(R(!0),w(""),!v.nomEmploye.trim())return void w("Veuillez sélectionner un employé");if(!N.find(e=>e.nomComplet===v.nomEmploye&&"actif"===e.statut))return void w("L'employé sélectionné n'est pas valide ou n'est plus actif");const t=Ma(v.montantCDF,"Le montant du paiement",{allowZero:!1,allowNegative:!1});if(!t.isValid)return void w(t.errors[0]||Na);const n={id:(null==C?void 0:C.id)||`emp_pay_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomEmploye:v.nomEmploye.trim(),montantCDF:v.montantCDF,montantUSD:v.montantCDF/T.tauxChangeUSDCDF,datePaiement:v.datePaiement,methodePaiement:v.methodePaiement,notes:null==(e=v.notes)?void 0:e.trim(),creePar:(null==Y?void 0:Y.nom)||"Système",dateCreation:(null==C?void 0:C.dateCreation)||(new Date).toISOString(),dateModification:C?(new Date).toISOString():void 0};C?(await ma.updateEmployeePayment(n),P("Paiement employé modifié avec succès")):(await ma.addEmployeePayment(n),P("Paiement employé ajouté avec succès")),await Z(),le()}catch(t){console.error("Error saving employee payment:",t),w("Erreur lors de la sauvegarde du paiement employé")}finally{R(!1)}},variant:"contained",disabled:A||!v.nomEmploye.trim()||v.montantCDF<=0||!N.find(e=>e.nomComplet===v.nomEmploye&&"actif"===e.statut),startIcon:A?r.jsx(qe,{size:20}):r.jsx(G,{}),children:C?"Modifier":"Ajouter"})]})]})]}),1===X&&r.jsxs(r.Fragment,{children:[r.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Employés"}),r.jsx(o,{variant:"h5",children:N.length})]})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Actifs"}),r.jsx(o,{variant:"h5",children:N.filter(e=>"actif"===e.statut).length})]})})}),r.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:r.jsx(ee,{children:r.jsxs(te,{children:[r.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Masse Salariale (CDF)"}),r.jsx(o,{variant:"h5",children:Ba(N.filter(e=>"actif"===e.statut).reduce((e,t)=>e+t.salaireCDF,0),"CDF")})]})})})]}),r.jsx(c,{sx:{p:2,mb:3},children:r.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(o,{variant:"h6",children:"Liste des Employés"}),H.canManageEmployeePayments&&r.jsx(I,{variant:"contained",startIcon:r.jsx(Pe,{}),onClick:()=>ue(),children:"Ajouter Employé"})]})}),r.jsx(c,{children:r.jsx(me,{children:r.jsxs(he,{children:[r.jsx(pe,{children:r.jsxs(xe,{children:[r.jsx(ge,{children:"Nom"}),r.jsx(ge,{children:"Poste"}),r.jsx(ge,{children:"Salaire (CDF)"}),r.jsx(ge,{children:"Date d'Embauche"}),r.jsx(ge,{children:"Statut"}),r.jsx(ge,{children:"Téléphone"}),H.canManageEmployeePayments&&r.jsx(ge,{children:"Actions"})]})}),r.jsx(ye,{children:0===N.length?r.jsx(xe,{children:r.jsx(ge,{colSpan:7,align:"center",children:r.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun employé trouvé"})})}):N.map(e=>r.jsxs(xe,{children:[r.jsx(ge,{children:r.jsxs(a,{display:"flex",alignItems:"center",children:[r.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomComplet," "]})}),r.jsx(ge,{children:e.poste}),r.jsx(ge,{children:r.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Ba(e.salaireCDF,"CDF")})}),r.jsx(ge,{children:Ut(new Date(e.dateEmbauche),"dd/MM/yyyy",{locale:Da})}),r.jsx(ge,{children:r.jsx(M,{label:e.statut,color:"actif"===e.statut?"success":"default",size:"small"})}),r.jsx(ge,{children:e.telephone||"-"}),H.canManageEmployeePayments&&r.jsx(ge,{children:r.jsxs(a,{display:"flex",gap:1,children:[r.jsx(F,{title:"Modifier",children:r.jsx(U,{size:"small",onClick:()=>ue(e),children:r.jsx(Ae,{fontSize:"small"})})}),r.jsx(F,{title:"Supprimer",children:r.jsx(U,{size:"small",color:"error",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'employé ${e.nomComplet} ?`))try{R(!0),await ma.deleteEmployee(e.id),P("Employé supprimé avec succès"),await ae()}catch(t){console.error("Error deleting employee:",t),w("Erreur lors de la suppression de l'employé")}finally{R(!1)}})(e),children:r.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]})})}),r.jsxs(Me,{open:V,onClose:Se,maxWidth:"md",fullWidth:!0,children:[r.jsx(Ie,{children:_?"Modifier Employé":"Ajouter Employé"}),r.jsxs(Ne,{children:[f&&r.jsx(O,{severity:"error",sx:{mb:2},children:f}),E&&r.jsx(O,{severity:"success",sx:{mb:2},children:E}),r.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Nom Complet *",value:z.nomComplet,onChange:e=>W({...z,nomComplet:e.target.value}),placeholder:"Ex: Jean Dupont"})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Poste *",value:z.poste,onChange:e=>W({...z,poste:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(Ya,{label:"Salaire",value:z.salaireCDF,onChange:e=>W({...z,salaireCDF:e}),min:0,max:1e7,step:1e3,exchangeRate:T.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Date d'Embauche",type:"date",value:z.dateEmbauche,onChange:e=>W({...z,dateEmbauche:e.target.value}),InputLabelProps:{shrink:!0}})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsxs(je,{fullWidth:!0,children:[r.jsx(De,{children:"Statut"}),r.jsxs(Ce,{value:z.statut,label:"Statut",onChange:e=>W({...z,statut:e.target.value}),children:[r.jsx(J,{value:"actif",children:"Actif"}),r.jsx(J,{value:"inactif",children:"Inactif"}),r.jsx(J,{value:"suspendu",children:"Suspendu"})]})]})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Téléphone",value:z.telephone,onChange:e=>W({...z,telephone:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,md:6,children:r.jsx(ne,{fullWidth:!0,label:"Adresse",value:z.adresse,onChange:e=>W({...z,adresse:e.target.value})})}),r.jsx(ce,{item:!0,xs:12,children:r.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:z.notes,onChange:e=>W({...z,notes:e.target.value})})})]})]}),r.jsxs(Le,{children:[r.jsx(I,{onClick:Se,children:"Annuler"}),r.jsx(I,{onClick:async()=>{var e,t,n;if(z.nomComplet.trim()&&z.poste.trim())try{R(!0);const r=(new Date).toISOString(),a={id:(null==_?void 0:_.id)||`emp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomComplet:z.nomComplet.trim(),poste:z.poste.trim(),salaireCDF:z.salaireCDF,salaireUSD:z.salaireCDF/T.tauxChangeUSDCDF,dateEmbauche:z.dateEmbauche,telephone:null==(e=z.telephone)?void 0:e.trim(),adresse:null==(t=z.adresse)?void 0:t.trim(),statut:z.statut,notes:null==(n=z.notes)?void 0:n.trim(),creePar:(null==Y?void 0:Y.nom)||"Système",dateCreation:(null==_?void 0:_.dateCreation)||r,dateModification:_?r:void 0};_?(await ma.updateEmployee(a),P("Employé modifié avec succès")):(await ma.addEmployee(a),P("Employé ajouté avec succès")),await ae(),Se()}catch(r){console.error("Error saving employee:",r),w("Erreur lors de la sauvegarde de l'employé")}finally{R(!1)}else w("Le nom complet et le poste sont requis")},variant:"contained",disabled:A||!z.nomComplet.trim()||!z.poste.trim(),startIcon:A?r.jsx(qe,{size:20}):r.jsx(lt,{}),children:_?"Modifier":"Ajouter"})]})]})]})]})},Di=()=>{var e,t,n,r,a,i,s,o,l;const c=(()=>{const e=Nr();return{components:{MuiButton:{styleOverrides:{root:{minHeight:e.isMobile?48:36,fontSize:e.isMobile?"1rem":"0.875rem"}}},MuiIconButton:{styleOverrides:{root:{padding:e.isMobile?12:8}}},MuiTableCell:{styleOverrides:{root:{padding:e.isMobile?"12px 8px":"16px"}}}}}})();return Ct({palette:{primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",...null==(n=null==(t=null==(e=c.components)?void 0:e.MuiButton)?void 0:t.styleOverrides)?void 0:n.root}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}},MuiIconButton:{styleOverrides:{root:{...null==(i=null==(a=null==(r=c.components)?void 0:r.MuiIconButton)?void 0:a.styleOverrides)?void 0:i.root}}},MuiTableCell:{styleOverrides:{root:{...null==(l=null==(o=null==(s=c.components)?void 0:s.MuiTableCell)?void 0:o.styleOverrides)?void 0:l.root}}}}},Sr)},Ci=()=>{const[e,t]=vt.useState(null),[n,i]=vt.useState(!0),[s,o]=vt.useState(null),[l,c]=vt.useState(()=>Di());vt.useEffect(()=>{const e=()=>{document.querySelectorAll("input, textarea, [contenteditable]").forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})};e();const t=()=>{setTimeout(e,50)};return window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),vt.useEffect(()=>{(async()=>{try{0;if("true"===localStorage.getItem("smartboutique_data_reset")){if(0===(await ma.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await ma.setUsers(e)}0}else await ma.initializeDefaultData();Nr().isMobile&&await ma.migrateFromDesktop(),await ha.initialize();const e=ha.getCurrentUser();t(e),i(!1)}catch(e){console.error("SmartBoutique: Error during initialization:",e),o(e instanceof Error?e.message:"Unknown error"),i(!1)}})()},[]);return s?r.jsxs(jt,{theme:l,children:[r.jsx(Dt,{}),r.jsxs(a,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,children:[r.jsx("h2",{children:"Erreur de chargement"}),r.jsx("p",{children:"Une erreur s'est produite lors du chargement de l'application:"}),r.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:s}),r.jsx("button",{onClick:()=>window.location.reload(),children:"Recharger l'application"})]})]}):n?r.jsxs(jt,{theme:l,children:[r.jsx(Dt,{}),r.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",children:"Chargement..."})]}):r.jsxs(jt,{theme:l,children:[r.jsx(Dt,{}),r.jsx(gr,{children:r.jsxs(hr,{children:[r.jsx(ur,{path:"/login",element:e?r.jsx(cr,{to:"/dashboard",replace:!0}):r.jsx(Pa,{onLogin:e=>{t(e)}})}),r.jsxs(ur,{path:"/",element:r.jsx(wa,{children:r.jsx(fa,{currentUser:e,onLogout:async()=>{await ha.logout(),t(null)}})}),children:[r.jsx(ur,{index:!0,element:r.jsx(cr,{to:"/dashboard",replace:!0})}),r.jsx(ur,{path:"dashboard",element:r.jsx(wa,{requiredPermission:"canViewDashboard",children:r.jsx(Ga,{})})}),r.jsx(ur,{path:"products",element:r.jsx(wa,{requiredPermission:"canViewProducts",children:r.jsx(Za,{})})}),r.jsx(ur,{path:"sales",element:r.jsx(wa,{requiredPermission:"canViewSales",children:r.jsx(ai,{})})}),r.jsx(ur,{path:"debts",element:r.jsx(wa,{requiredPermission:"canViewDebts",children:r.jsx(ii,{})})}),r.jsx(ur,{path:"expenses",element:r.jsx(wa,{requiredPermission:"canViewExpenses",children:r.jsx(oi,{})})}),r.jsx(ur,{path:"employee-payments",element:r.jsx(wa,{requiredPermission:"canViewEmployeePayments",children:r.jsx(ji,{})})}),r.jsx(ur,{path:"reports",element:r.jsx(wa,{requiredPermission:"canViewReports",children:r.jsx(ci,{})})}),r.jsx(ur,{path:"users",element:r.jsx(wa,{requiredPermission:"canViewUsers",children:r.jsx(di,{})})}),r.jsx(ur,{path:"settings",element:r.jsx(wa,{requiredPermission:"canViewSettings",children:r.jsx(yi,{})})})]}),r.jsx(ur,{path:"*",element:r.jsx(cr,{to:"/dashboard",replace:!0})})]})})]})};class Si extends vt.Component{constructor(e){super(e),n(this,"handleReload",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.reload()}),n(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})}),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?r.jsx(a,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,bgcolor:"#f5f5f5",children:r.jsxs(c,{elevation:3,sx:{p:4,maxWidth:600,width:"100%"},children:[r.jsx(o,{variant:"h4",color:"error",gutterBottom:!0,children:"Oops! Une erreur s'est produite"}),r.jsx(o,{variant:"body1",paragraph:!0,children:"L'application a rencontré une erreur inattendue. Vous pouvez essayer de continuer ou recharger l'application."}),!1,r.jsxs(a,{display:"flex",gap:2,mt:3,children:[r.jsx(I,{variant:"contained",color:"primary",onClick:this.handleReset,children:"Continuer"}),r.jsx(I,{variant:"outlined",color:"secondary",onClick:this.handleReload,children:"Recharger l'application"})]})]})}):this.props.children}}Xt.register(Qt,Jt,Ht,Gt,Yt,Kt,Zt,en,tn);const vi=document.getElementById("root");vi?an.createRoot(vi).render(r.jsx(ft.StrictMode,{children:r.jsx(Si,{children:r.jsx(Ci,{})})})):console.error("SmartBoutique: Root element not found!");export{Tr as W};
